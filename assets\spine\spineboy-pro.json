{"skeleton": {"hash": "itfFESDjM1c", "spine": "4.1.17", "x": -188.63, "y": -7.94, "width": 418.45, "height": 686.2, "images": "./images/", "audio": ""}, "bones": [{"name": "root", "rotation": 0.05}, {"name": "hip", "parent": "root", "y": 247.27}, {"name": "crosshair", "parent": "root", "x": 302.83, "y": 569.45, "color": "ff3f00ff"}, {"name": "aim-constraint-target", "parent": "hip", "length": 26.24, "rotation": 19.61, "x": 1.02, "y": 5.62, "color": "abe323ff"}, {"name": "rear-foot-target", "parent": "root", "x": 61.91, "y": 0.42, "color": "ff3f00ff"}, {"name": "rear-leg-target", "parent": "rear-foot-target", "x": -33.91, "y": 37.34, "color": "ff3f00ff"}, {"name": "rear-thigh", "parent": "hip", "length": 85.72, "rotation": -72.54, "x": 8.91, "y": -5.63, "color": "ff000dff"}, {"name": "rear-shin", "parent": "rear-thigh", "length": 121.88, "rotation": -19.83, "x": 86.1, "y": -1.33, "color": "ff000dff"}, {"name": "rear-foot", "parent": "rear-shin", "length": 51.58, "rotation": 45.78, "x": 121.46, "y": -0.76, "color": "ff000dff"}, {"name": "back-foot-tip", "parent": "rear-foot", "length": 50.3, "rotation": -0.85, "x": 51.17, "y": 0.24, "transform": "noRotationOrReflection", "color": "ff000dff"}, {"name": "board-ik", "parent": "root", "x": -131.78, "y": 69.09, "color": "4c56ffff"}, {"name": "clipping", "parent": "root"}, {"name": "hoverboard-controller", "parent": "root", "rotation": -0.28, "x": -329.69, "y": 69.82, "color": "ff0004ff"}, {"name": "exhaust1", "parent": "hoverboard-controller", "rotation": 3.02, "x": -249.68, "y": 53.39}, {"name": "exhaust2", "parent": "hoverboard-controller", "rotation": 26.34, "x": -191.6, "y": -22.92}, {"name": "exhaust3", "parent": "hoverboard-controller", "rotation": -12.34, "x": -236.03, "y": 80.54, "scaleX": 0.7847, "scaleY": 0.7847}, {"name": "portal-root", "parent": "root", "x": 12.9, "y": 328.54, "scaleX": 2.0334, "scaleY": 2.0334}, {"name": "flare1", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare10", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare2", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare3", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare4", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare5", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare6", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare7", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare8", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare9", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "torso", "parent": "hip", "length": 42.52, "rotation": 103.82, "x": -1.62, "y": 4.9, "color": "e0da19ff"}, {"name": "torso2", "parent": "torso", "length": 42.52, "x": 42.52, "color": "e0da19ff"}, {"name": "torso3", "parent": "torso2", "length": 42.52, "x": 42.52, "color": "e0da19ff"}, {"name": "front-shoulder", "parent": "torso3", "rotation": 255.89, "x": 18.72, "y": 19.33, "color": "00ff04ff"}, {"name": "front-upper-arm", "parent": "front-shoulder", "length": 69.45, "rotation": -87.51, "color": "00ff04ff"}, {"name": "front-bracer", "parent": "front-upper-arm", "length": 40.57, "rotation": 18.3, "x": 68.8, "y": -0.68, "color": "00ff04ff"}, {"name": "front-fist", "parent": "front-bracer", "length": 65.39, "rotation": 12.43, "x": 40.57, "y": 0.2, "color": "00ff04ff"}, {"name": "front-foot-target", "parent": "root", "x": -13.53, "y": 0.04, "color": "ff3f00ff"}, {"name": "front-leg-target", "parent": "front-foot-target", "x": -28.4, "y": 29.06, "color": "ff3f00ff"}, {"name": "front-thigh", "parent": "hip", "length": 74.81, "rotation": -95.51, "x": -17.46, "y": -11.64, "color": "00ff04ff"}, {"name": "front-shin", "parent": "front-thigh", "length": 128.77, "rotation": -2.21, "x": 78.69, "y": 1.6, "color": "00ff04ff"}, {"name": "front-foot", "parent": "front-shin", "length": 41.01, "rotation": 51.27, "x": 128.76, "y": -0.34, "color": "00ff04ff"}, {"name": "front-foot-tip", "parent": "front-foot", "length": 56.03, "rotation": -1.68, "x": 41.42, "y": -0.09, "transform": "noRotationOrReflection", "color": "00ff04ff"}, {"name": "back-shoulder", "parent": "torso3", "rotation": -104.11, "x": 7.32, "y": -19.22, "color": "ff000dff"}, {"name": "rear-upper-arm", "parent": "back-shoulder", "length": 51.94, "rotation": -65.45, "color": "ff000dff"}, {"name": "rear-bracer", "parent": "rear-upper-arm", "length": 34.56, "rotation": 23.15, "x": 51.36, "color": "ff000dff"}, {"name": "gun", "parent": "rear-bracer", "length": 43.11, "rotation": -5.43, "x": 34.42, "y": -0.45, "color": "ff000dff"}, {"name": "gun-tip", "parent": "gun", "rotation": 7.1, "x": 200.78, "y": 52.5, "color": "ff0000ff"}, {"name": "neck", "parent": "torso3", "length": 25.45, "rotation": -31.54, "x": 42.46, "y": -0.31, "color": "e0da19ff"}, {"name": "head", "parent": "neck", "length": 131.79, "rotation": 26.1, "x": 27.66, "y": -0.26, "color": "e0da19ff"}, {"name": "hair1", "parent": "head", "length": 47.23, "rotation": -49.1, "x": 149.83, "y": -59.77, "color": "e0da19ff"}, {"name": "hair2", "parent": "hair1", "length": 55.57, "rotation": 50.42, "x": 47.23, "y": 0.19, "color": "e0da19ff"}, {"name": "hair3", "parent": "head", "length": 62.22, "rotation": -32.17, "x": 164.14, "y": 3.68, "color": "e0da19ff"}, {"name": "hair4", "parent": "hair3", "length": 80.28, "rotation": 83.71, "x": 62.22, "y": -0.04, "color": "e0da19ff"}, {"name": "hoverboard-thruster-front", "parent": "hoverboard-controller", "rotation": -29.2, "x": 95.77, "y": -2.99, "transform": "noRotationOrReflection"}, {"name": "hoverboard-thruster-rear", "parent": "hoverboard-controller", "rotation": -29.2, "x": -76.47, "y": -4.88, "transform": "noRotationOrReflection"}, {"name": "hoverglow-front", "parent": "hoverboard-thruster-front", "rotation": 0.17, "x": -1.78, "y": -37.79}, {"name": "hoverglow-rear", "parent": "hoverboard-thruster-rear", "rotation": 0.17, "x": 1.06, "y": -35.66}, {"name": "muzzle", "parent": "rear-bracer", "rotation": 3.06, "x": 242.34, "y": 34.26, "color": "ffb900ff"}, {"name": "muzzle-ring", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring2", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring3", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring4", "parent": "muzzle", "color": "ffb900ff"}, {"name": "portal", "parent": "portal-root"}, {"name": "portal-shade", "parent": "portal-root"}, {"name": "portal-streaks1", "parent": "portal-root"}, {"name": "portal-streaks2", "parent": "portal-root"}, {"name": "side-glow1", "parent": "hoverboard-controller", "x": -110.56, "y": 2.62, "color": "000effff"}, {"name": "side-glow2", "parent": "hoverboard-controller", "x": -110.56, "y": 2.62, "scaleX": 0.738, "scaleY": 0.738, "color": "000effff"}, {"name": "head-control", "parent": "head", "x": 110.21, "color": "00a220ff"}], "slots": [{"name": "portal-bg", "bone": "portal"}, {"name": "portal-shade", "bone": "portal-shade"}, {"name": "portal-streaks2", "bone": "portal-streaks2", "blend": "additive"}, {"name": "portal-streaks1", "bone": "portal-streaks1", "blend": "additive"}, {"name": "portal-flare8", "bone": "flare8", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare9", "bone": "flare9", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare10", "bone": "flare10", "color": "c3cbffff", "blend": "additive"}, {"name": "clipping", "bone": "clipping"}, {"name": "exhaust3", "bone": "exhaust3", "color": "5eb4ffff", "blend": "additive"}, {"name": "hoverboard-thruster-rear", "bone": "hoverboard-thruster-rear"}, {"name": "hoverboard-thruster-front", "bone": "hoverboard-thruster-front"}, {"name": "hoverboard-board", "bone": "hoverboard-controller"}, {"name": "side-glow1", "bone": "side-glow1", "color": "ff8686ff", "blend": "additive"}, {"name": "side-glow3", "bone": "side-glow1", "color": "ff8686ff", "blend": "additive"}, {"name": "side-glow2", "bone": "side-glow2", "color": "ff8686ff", "blend": "additive"}, {"name": "hoverglow-front", "bone": "hoverglow-front", "color": "5eb4ffff", "blend": "additive"}, {"name": "hoverglow-rear", "bone": "hoverglow-rear", "color": "5eb4ffff", "blend": "additive"}, {"name": "exhaust1", "bone": "exhaust2", "color": "5eb4ffff", "blend": "additive"}, {"name": "exhaust2", "bone": "exhaust1", "color": "5eb4ffff", "blend": "additive"}, {"name": "rear-upper-arm", "bone": "rear-upper-arm", "attachment": "rear-upper-arm"}, {"name": "rear-bracer", "bone": "rear-bracer", "attachment": "rear-bracer"}, {"name": "gun", "bone": "gun", "attachment": "gun"}, {"name": "rear-foot", "bone": "rear-foot", "attachment": "rear-foot"}, {"name": "rear-thigh", "bone": "rear-thigh", "attachment": "rear-thigh"}, {"name": "rear-shin", "bone": "rear-shin", "attachment": "rear-shin"}, {"name": "neck", "bone": "neck", "attachment": "neck"}, {"name": "torso", "bone": "torso", "attachment": "torso"}, {"name": "front-upper-arm", "bone": "front-upper-arm", "attachment": "front-upper-arm"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye", "bone": "head", "attachment": "eye-indifferent"}, {"name": "front-thigh", "bone": "front-thigh", "attachment": "front-thigh"}, {"name": "front-foot", "bone": "front-foot", "attachment": "front-foot"}, {"name": "front-shin", "bone": "front-shin", "attachment": "front-shin"}, {"name": "mouth", "bone": "head", "attachment": "mouth-smile"}, {"name": "goggles", "bone": "head", "attachment": "goggles"}, {"name": "front-bracer", "bone": "front-bracer", "attachment": "front-bracer"}, {"name": "front-fist", "bone": "front-fist", "attachment": "front-fist-closed"}, {"name": "muzzle", "bone": "muzzle"}, {"name": "head-bb", "bone": "head"}, {"name": "portal-flare1", "bone": "flare1", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare2", "bone": "flare2", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare3", "bone": "flare3", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare4", "bone": "flare4", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare5", "bone": "flare5", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare6", "bone": "flare6", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare7", "bone": "flare7", "color": "c3cbffff", "blend": "additive"}, {"name": "crosshair", "bone": "crosshair"}, {"name": "muzzle-glow", "bone": "gun-tip", "color": "ffffff00", "blend": "additive"}, {"name": "muzzle-ring", "bone": "muzzle-ring", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring2", "bone": "muzzle-ring2", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring3", "bone": "muzzle-ring3", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring4", "bone": "muzzle-ring4", "color": "d8baffff", "blend": "additive"}], "ik": [{"name": "aim-ik", "order": 13, "bones": ["rear-upper-arm"], "target": "crosshair", "mix": 0}, {"name": "aim-torso-ik", "order": 8, "bones": ["aim-constraint-target"], "target": "crosshair"}, {"name": "board-ik", "order": 1, "bones": ["hoverboard-controller"], "target": "board-ik"}, {"name": "front-foot-ik", "order": 6, "bones": ["front-foot"], "target": "front-foot-target"}, {"name": "front-leg-ik", "order": 4, "bones": ["front-thigh", "front-shin"], "target": "front-leg-target", "bendPositive": false}, {"name": "rear-foot-ik", "order": 7, "bones": ["rear-foot"], "target": "rear-foot-target"}, {"name": "rear-leg-ik", "order": 5, "bones": ["rear-thigh", "rear-shin"], "target": "rear-leg-target", "bendPositive": false}], "transform": [{"name": "aim-front-arm-transform", "order": 11, "bones": ["front-upper-arm"], "target": "aim-constraint-target", "rotation": -180, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "aim-head-transform", "order": 10, "bones": ["head"], "target": "aim-constraint-target", "rotation": 84.3, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "aim-rear-arm-transform", "order": 12, "bones": ["rear-upper-arm"], "target": "aim-constraint-target", "x": 57.7, "y": 56.4, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "aim-torso-transform", "order": 9, "bones": ["torso"], "target": "aim-constraint-target", "rotation": 69.5, "shearY": -36, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "front-foot-board-transform", "order": 2, "bones": ["front-foot-target"], "target": "hoverboard-controller", "x": -69.8, "y": 20.7, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "rear-foot-board-transform", "order": 3, "bones": ["rear-foot-target"], "target": "hoverboard-controller", "x": 86.6, "y": 21.3, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "shoulder", "bones": ["back-shoulder"], "target": "front-shoulder", "x": 40.17, "y": -1.66, "mixRotate": 0, "mixX": -1, "mixScaleX": 0, "mixShearY": 0}, {"name": "toes-board", "order": 14, "bones": ["front-foot-tip", "back-foot-tip"], "target": "hoverboard-controller", "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"clipping": {"clipping": {"type": "clipping", "end": "head-bb", "vertexCount": 9, "vertices": [66.76, 509.48, 19.98, 434.54, 5.34, 336.28, 22.19, 247.93, 77.98, 159.54, 182.21, -97.56, 1452.26, -99.8, 1454.33, 843.61, 166.57, 841.02], "color": "ce3a3aff"}}, "crosshair": {"crosshair": {"width": 89, "height": 89}}, "exhaust1": {"hoverglow-small": {"scaleX": 0.4629, "scaleY": 0.8129, "rotation": -83.07, "width": 274, "height": 75}}, "exhaust2": {"hoverglow-small": {"x": 0.01, "y": -0.76, "scaleX": 0.4208, "scaleY": 0.8403, "rotation": -89.25, "width": 274, "height": 75}}, "exhaust3": {"hoverglow-small": {"scaleX": 0.4629, "scaleY": 0.8129, "rotation": -83.07, "width": 274, "height": 75}}, "eye": {"eye-indifferent": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 66, -36.8, -91.35, 0.3, 46, 73.41, -91.35, 0.7, 2, 66, -87.05, -13.11, 0.70968, 46, 23.16, -13.11, 0.29032, 2, 66, -12.18, 34.99, 0.82818, 46, 98.03, 34.99, 0.17182, 2, 66, 38.07, -43.25, 0.59781, 46, 148.28, -43.25, 0.40219], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 89}, "eye-surprised": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 66, -46.74, -89.7, 0.3, 46, 63.47, -89.7, 0.7, 2, 66, -77.58, -1.97, 0.71, 46, 32.63, -1.97, 0.29, 2, 66, 6.38, 27.55, 0.83, 46, 116.59, 27.55, 0.17, 2, 66, 37.22, -60.19, 0.6, 46, 147.44, -60.19, 0.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 89}}, "front-bracer": {"front-bracer": {"x": 12.03, "y": -1.68, "rotation": 79.6, "width": 58, "height": 80}}, "front-fist": {"front-fist-closed": {"x": 35.5, "y": 6, "rotation": 67.16, "width": 75, "height": 82}, "front-fist-open": {"x": 39.57, "y": 7.76, "rotation": 67.16, "width": 86, "height": 87}}, "front-foot": {"front-foot": {"type": "mesh", "uvs": [0.59417, 0.23422, 0.62257, 0.30336, 0.6501, 0.37036, 0.67637, 0.38404, 0.72068, 0.4071, 0.76264, 0.42894, 1, 0.70375, 1, 1, 0.65517, 1, 0.46923, 0.99999, 0, 1, 0, 0.39197, 0.17846, 0, 0.49796, 0], "triangles": [8, 9, 3, 4, 8, 3, 5, 8, 4, 6, 8, 5, 8, 6, 7, 11, 1, 10, 0, 12, 13, 0, 11, 12, 0, 1, 11, 9, 2, 3, 1, 2, 10, 9, 10, 2], "vertices": [2, 38, 18.17, 41.57, 0.7896, 39, 12.46, 46.05, 0.2104, 2, 38, 24.08, 40.76, 0.71228, 39, 16.12, 41.34, 0.28772, 2, 38, 29.81, 39.98, 0.55344, 39, 19.67, 36.78, 0.44656, 2, 38, 32.81, 41.67, 0.38554, 39, 23, 35.89, 0.61446, 2, 38, 37.86, 44.52, 0.25567, 39, 28.61, 34.4, 0.74433, 2, 38, 42.65, 47.22, 0.17384, 39, 33.92, 32.99, 0.82616, 1, 39, 64.15, 14.56, 1, 1, 39, 64.51, -5.87, 1, 1, 39, 21.08, -6.64, 1, 2, 38, 44.67, -6.77, 0.5684, 39, -2.34, -6.97, 0.4316, 1, 38, 3.1, -48.81, 1, 1, 38, -26.73, -19.31, 1, 1, 38, -30.15, 15.69, 1, 1, 38, -1.84, 44.32, 1], "hull": 14, "edges": [14, 16, 16, 18, 18, 20, 4, 18, 20, 22, 24, 26, 22, 24, 12, 14, 10, 12, 2, 4, 2, 20, 4, 6, 6, 16, 2, 0, 0, 26, 6, 8, 8, 10], "width": 126, "height": 69}}, "front-shin": {"front-shin": {"type": "mesh", "uvs": [0.90031, 0.05785, 1, 0.12828, 1, 0.21619, 0.9025, 0.31002, 0.78736, 0.35684, 0.78081, 0.39874, 0.77215, 0.45415, 0.77098, 0.51572, 0.84094, 0.63751, 0.93095, 0.7491, 0.95531, 0.7793, 0.78126, 0.87679, 0.5613, 1, 0.2687, 1, 0, 1, 0.00279, 0.96112, 0.01358, 0.81038, 0.02822, 0.60605, 0.08324, 0.45142, 0.18908, 0.31882, 0.29577, 0.2398, 0.30236, 0.14941, 0.37875, 0.05902, 0.53284, 0, 0.70538, 0, 0.41094, 0.71968, 0.40743, 0.54751, 0.41094, 0.4536, 0.4724, 0.35186, 0.33367, 0.27829, 0.50226, 0.31664, 0.65328, 0.67507, 0.60762, 0.52716, 0.6006, 0.45125, 0.62747, 0.37543, 0.6573, 0.3385, 0.27843, 0.32924, 0.18967, 0.45203, 0.16509, 0.58586, 0.18265, 0.7682, 0.50532, 0.24634, 0.59473, 0.17967, 0.60161, 0.10611, 0.51392, 0.04327, 0.72198, 0.28849, 0.82343, 0.20266, 0.86814, 0.11377, 0.79592, 0.04634, 0.44858, 0.15515, 0.25466, 0.96219, 0.53169, 0.9448, 0.7531, 0.8324], "triangles": [24, 0, 47, 43, 23, 24, 47, 43, 24, 43, 22, 23, 42, 43, 47, 46, 47, 0, 42, 47, 46, 46, 0, 1, 48, 22, 43, 48, 43, 42, 21, 22, 48, 41, 48, 42, 45, 42, 46, 41, 42, 45, 46, 1, 2, 45, 46, 2, 40, 48, 41, 48, 20, 21, 29, 48, 40, 29, 20, 48, 44, 41, 45, 40, 41, 44, 3, 45, 2, 44, 45, 3, 30, 29, 40, 35, 30, 40, 36, 19, 20, 36, 20, 29, 44, 35, 40, 28, 29, 30, 4, 44, 3, 35, 44, 4, 34, 30, 35, 5, 35, 4, 34, 28, 30, 33, 28, 34, 37, 19, 36, 18, 19, 37, 27, 29, 28, 27, 28, 33, 36, 29, 27, 37, 36, 27, 5, 34, 35, 6, 34, 5, 33, 34, 6, 6, 32, 33, 7, 32, 6, 26, 37, 27, 38, 18, 37, 38, 37, 26, 17, 18, 38, 31, 32, 7, 31, 7, 8, 32, 25, 26, 38, 26, 25, 27, 33, 32, 32, 26, 27, 39, 38, 25, 17, 38, 39, 16, 17, 39, 51, 31, 8, 51, 8, 9, 11, 51, 9, 11, 9, 10, 31, 50, 25, 31, 25, 32, 50, 31, 51, 49, 39, 25, 49, 25, 50, 15, 16, 39, 49, 15, 39, 13, 49, 50, 14, 15, 49, 13, 14, 49, 12, 50, 51, 12, 51, 11, 13, 50, 12], "vertices": [-23.66, 19.37, -11.73, 28.98, 4.34, 30.83, 22.41, 24.87, 32.05, 16.48, 39.77, 16.83, 49.98, 17.3, 61.25, 18.5, 82.85, 26.78, 102.4, 36.46, 107.69, 39.09, 127.15, 26.97, 151.74, 11.65, 154.49, -12.18, 157.02, -34.07, 149.89, -34.66, 122.23, -36.97, 84.75, -40.09, 55.97, -38.88, 30.73, -33.05, 15.29, -26.03, -1.3, -27.41, -18.54, -23.09, -30.78, -11.79, -32.4, 2.27, 101.92, -6.52, 70.48, -10.44, 53.28, -12.14, 34.11, -9.28, 21.96, -22.13, 27.39, -7.59, 91.48, 12.28, 64.88, 5.44, 51.07, 3.26, 36.95, 3.85, 29.92, 5.5, 31.8, -25.56, 55.08, -30.19, 79.77, -29.37, 112.93, -24.09, 14.51, -8.83, 1.48, -2.95, -12.03, -3.94, -22.69, -12.41, 20.17, 9.71, 3.53, 16.16, -13.14, 17.93, -24.78, 10.62, -1.62, -15.37, 147.71, -14.13, 141.93, 8.07, 119.3, 23.74], "hull": 25, "edges": [8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 46, 48, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 32, 34, 50, 52, 52, 54, 54, 56, 40, 58, 58, 60, 8, 10, 20, 22, 22, 24, 62, 64, 64, 66, 66, 68, 8, 70, 70, 60, 68, 70, 58, 72, 72, 74, 74, 76, 76, 78, 24, 26, 26, 28, 58, 80, 80, 82, 82, 84, 84, 86, 86, 44, 70, 88, 88, 90, 90, 92, 92, 94, 94, 48, 80, 88, 88, 6, 82, 90, 90, 4, 84, 92, 92, 2, 86, 94, 94, 0, 56, 60, 10, 12, 12, 14, 14, 16, 28, 30, 30, 32, 26, 98, 98, 78, 30, 98, 24, 100, 100, 50, 98, 100, 22, 102, 102, 62, 100, 102, 16, 18, 18, 20, 102, 18], "width": 82, "height": 184}}, "front-thigh": {"front-thigh": {"x": 42.48, "y": 4.45, "rotation": 84.87, "width": 45, "height": 112}}, "front-upper-arm": {"front-upper-arm": {"x": 28.31, "y": 7.37, "rotation": 97.9, "width": 46, "height": 97}}, "goggles": {"goggles": {"type": "mesh", "uvs": [0.53653, 0.04114, 0.72922, 0.16036, 0.91667, 0.33223, 0.97046, 0.31329, 1, 0.48053, 0.95756, 0.5733, 0.88825, 0.6328, 0.86878, 0.78962, 0.77404, 0.8675, 0.72628, 1, 0.60714, 0.93863, 0.49601, 0.88138, 0.41558, 0.75027, 0.32547, 0.70084, 0.2782, 0.58257, 0.1721, 0.63281, 0.17229, 0.75071, 0.10781, 0.79898, 0, 0.32304, 0, 0.12476, 0.07373, 0.07344, 0.15423, 0.10734, 0.23165, 0.13994, 0.30313, 0.02256, 0.34802, 0, 0.42979, 0.69183, 0.39476, 0.51042, 0.39488, 0.31512, 0.45878, 0.23198, 0.56501, 0.28109, 0.69961, 0.39216, 0.82039, 0.54204, 0.85738, 0.62343, 0.91107, 0.51407, 0.72639, 0.32147, 0.58764, 0.19609, 0.48075, 0.11269, 0.37823, 0.05501, 0.3287, 0.17866, 0.319, 0.305, 0.36036, 0.53799, 0.40327, 0.70072, 0.30059, 0.55838, 0.21957, 0.2815, 0.09963, 0.28943, 0.56863, 0.4368, 0.4911, 0.37156, 0.51185, 0.52093, 0.67018, 0.59304, 0.7619, 0.68575, 0.73296, 0.43355], "triangles": [18, 44, 15, 21, 19, 20, 17, 18, 15, 44, 19, 21, 2, 3, 4, 18, 19, 44, 2, 33, 34, 33, 2, 4, 5, 33, 4, 5, 6, 33, 7, 32, 6, 31, 50, 33, 32, 31, 33, 6, 32, 33, 31, 49, 50, 49, 31, 32, 49, 32, 7, 8, 49, 7, 33, 50, 34, 17, 15, 16, 9, 48, 8, 49, 48, 50, 50, 48, 45, 47, 45, 48, 50, 45, 30, 45, 47, 46, 45, 46, 29, 30, 45, 29, 30, 29, 34, 30, 34, 50, 47, 26, 46, 25, 10, 11, 12, 25, 11, 41, 12, 42, 42, 44, 43, 43, 21, 22, 41, 40, 25, 41, 42, 40, 29, 35, 34, 40, 26, 25, 25, 26, 47, 37, 24, 0, 36, 37, 0, 42, 43, 39, 42, 39, 40, 28, 38, 36, 40, 39, 26, 28, 27, 38, 26, 39, 27, 37, 38, 23, 39, 43, 38, 38, 37, 36, 27, 39, 38, 43, 22, 38, 37, 23, 24, 22, 23, 38, 36, 0, 35, 28, 36, 35, 29, 28, 35, 27, 28, 46, 26, 27, 46, 35, 0, 1, 34, 35, 1, 12, 41, 25, 47, 10, 25, 44, 21, 43, 42, 14, 44, 14, 15, 44, 13, 14, 42, 12, 13, 42, 46, 28, 29, 47, 48, 10, 48, 9, 10, 49, 8, 48, 2, 34, 1], "vertices": [2, 66, 61.88, 22.81, 0.832, 46, 172.09, 22.81, 0.168, 2, 66, 59.89, -31.19, 0.6855, 46, 170.1, -31.19, 0.3145, 2, 66, 49.2, -86.8, 0.32635, 46, 159.41, -86.8, 0.67365, 2, 66, 56.82, -99.01, 0.01217, 46, 167.03, -99.01, 0.98783, 1, 46, 143.4, -115.48, 1, 2, 66, 15, -110.14, 0.0041, 46, 125.21, -110.14, 0.9959, 2, 66, -0.32, -96.36, 0.07948, 46, 109.89, -96.36, 0.92052, 2, 66, -26.56, -100.19, 0.01905, 46, 83.65, -100.19, 0.98095, 2, 66, -46.96, -81.16, 0.4921, 46, 63.26, -81.16, 0.50791, 2, 66, -71.84, -76.69, 0.56923, 46, 38.37, -76.69, 0.43077, 2, 66, -72.54, -43.98, 0.74145, 46, 37.67, -43.98, 0.25855, 2, 66, -73.2, -13.47, 0.87929, 46, 37.01, -13.47, 0.12071, 2, 66, -59.63, 13.55, 0.864, 46, 50.58, 13.55, 0.136, 2, 66, -59.69, 38.45, 0.85289, 46, 50.52, 38.45, 0.14711, 2, 66, -45.26, 56.6, 0.74392, 46, 64.95, 56.6, 0.25608, 2, 66, -62.31, 79.96, 0.624, 46, 47.9, 79.96, 0.376, 2, 66, -80.76, 73.42, 0.616, 46, 29.45, 73.42, 0.384, 2, 66, -93.9, 86.64, 0.288, 46, 16.31, 86.64, 0.712, 1, 46, 81.51, 139.38, 1, 1, 46, 112.56, 150.3, 1, 2, 66, 16.76, 134.97, 0.02942, 46, 126.97, 134.97, 0.97058, 2, 66, 18.42, 113.28, 0.36147, 46, 128.63, 113.28, 0.63853, 2, 66, 20.02, 92.43, 0.7135, 46, 130.23, 92.43, 0.2865, 2, 66, 44.58, 81.29, 0.69603, 46, 154.79, 81.29, 0.30397, 2, 66, 52, 71.48, 0.848, 46, 162.21, 71.48, 0.152, 2, 66, -49.25, 13.27, 0.8, 46, 60.96, 13.27, 0.2, 2, 66, -23.88, 31.88, 0.896, 46, 86.33, 31.88, 0.104, 2, 66, 6.72, 42.6, 0.928, 46, 116.93, 42.6, 0.072, 2, 66, 25.26, 31.44, 0.8, 46, 135.47, 31.44, 0.2, 2, 66, 26.77, 2.59, 0.75, 46, 136.98, 2.59, 0.25, 2, 66, 21.02, -36.66, 0.54887, 46, 131.23, -36.66, 0.45113, 2, 66, 8.01, -74.65, 0.36029, 46, 118.22, -74.65, 0.63971, 2, 66, -1.52, -88.24, 0.1253, 46, 108.69, -88.24, 0.8747, 2, 66, 20.25, -95.44, 0.08687, 46, 130.46, -95.44, 0.91313, 2, 66, 34.42, -39.36, 0.72613, 46, 144.63, -39.36, 0.27387, 2, 66, 42.03, 1.7, 0.824, 46, 152.25, 1.7, 0.176, 2, 66, 45.85, 32.6, 0.856, 46, 156.06, 32.6, 0.144, 1, 66, 46.01, 61.02, 1, 1, 66, 22.35, 66.41, 1, 1, 66, 1.73, 61.84, 1, 2, 66, -31.17, 38.83, 0.928, 46, 79.04, 38.83, 0.072, 2, 66, -52.94, 19.31, 0.79073, 46, 57.27, 19.31, 0.20927, 2, 66, -39.54, 52.42, 0.912, 46, 70.67, 52.42, 0.088, 2, 66, -3.2, 87.61, 0.744, 46, 107.02, 87.61, 0.256, 2, 66, -14.82, 116.7, 0.6368, 46, 95.4, 116.7, 0.3632, 2, 66, 2.7, -6.87, 0.856, 46, 112.91, -6.87, 0.144, 2, 66, 6.21, 15.8, 0.744, 46, 116.42, 15.8, 0.256, 2, 66, -15.39, 2.47, 0.856, 46, 94.82, 2.47, 0.144, 2, 66, -12.98, -40.48, 0.72102, 46, 97.24, -40.48, 0.27898, 2, 66, -19.55, -68.16, 0.59162, 46, 90.66, -68.16, 0.40838, 2, 66, 17.44, -47.15, 0.53452, 46, 127.65, -47.15, 0.46548], "hull": 25, "edges": [36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 48, 46, 46, 44, 36, 38, 40, 38, 24, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 12, 8, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 24, 24, 84, 84, 86, 86, 44, 40, 42, 42, 44, 42, 88, 88, 30, 58, 90, 90, 92, 92, 94, 18, 20, 20, 22, 94, 20, 18, 96, 96, 98, 60, 100, 100, 62, 98, 100], "width": 261, "height": 166}}, "gun": {"gun": {"x": 77.3, "y": 16.4, "rotation": 60.83, "width": 210, "height": 203}}, "head": {"head": {"type": "mesh", "uvs": [0.75919, 0.06107, 0.88392, 0.17893, 0.90174, 0.30856, 0.94224, 0.1966, 1, 0.26584, 1, 0.422, 0.95864, 0.46993, 0.92118, 0.51333, 0.85957, 0.5347, 0.78388, 0.65605, 0.74384, 0.74838, 0.85116, 0.75151, 0.84828, 0.82564, 0.81781, 0.85367, 0.75599, 0.85906, 0.76237, 0.90468, 0.65875, 1, 0.38337, 1, 0.1858, 0.85404, 0.12742, 0.81091, 0.06025, 0.69209, 0, 0.58552, 0, 0.41021, 0.0853, 0.20692, 0.24243, 0.14504, 0.5, 0.1421, 0.50324, 0.07433, 0.41738, 0, 0.57614, 0, 0.85059, 0.36087, 0.73431, 0.43206, 0.68481, 0.31271, 0.72165, 0.16718, 0.55931, 0.04154, 0.44764, 0.22895, 0.23926, 0.26559, 0.71272, 0.44036, 0.56993, 0.383, 0.41678, 0.33511, 0.293, 0.31497, 0.70802, 0.44502, 0.56676, 0.38976, 0.41521, 0.34416, 0.28754, 0.33017, 0.88988, 0.50177, 0.30389, 0.73463, 0.2646, 0.65675, 0.21414, 0.61584, 0.14613, 0.62194, 0.10316, 0.66636, 0.10358, 0.72557, 0.14505, 0.79164, 0.20263, 0.81355, 0.27873, 0.80159, 0.34947, 0.7376, 0.23073, 0.57073, 0.08878, 0.60707, 0.29461, 0.8129, 0.73006, 0.87883, 0.69805, 0.87348, 0.66166, 0.79681, 0.22468, 0.69824, 0.14552, 0.67405], "triangles": [50, 49, 62, 34, 25, 31, 39, 35, 34, 38, 39, 34, 37, 38, 34, 42, 39, 38, 43, 39, 42, 32, 2, 31, 31, 37, 34, 42, 38, 37, 41, 42, 37, 43, 22, 39, 30, 31, 29, 36, 37, 31, 30, 36, 31, 40, 41, 37, 36, 40, 37, 36, 30, 44, 55, 22, 43, 55, 48, 56, 47, 48, 55, 46, 55, 54, 42, 55, 43, 47, 55, 46, 62, 49, 48, 61, 47, 46, 62, 48, 47, 61, 62, 47, 46, 54, 45, 42, 41, 55, 61, 46, 45, 55, 41, 54, 61, 51, 50, 61, 50, 62, 60, 41, 40, 54, 41, 60, 53, 61, 45, 52, 51, 61, 57, 53, 45, 57, 45, 54, 53, 52, 61, 52, 19, 51, 57, 18, 52, 57, 52, 53, 17, 54, 60, 57, 54, 17, 18, 57, 17, 19, 50, 51, 33, 27, 28, 26, 27, 33, 0, 33, 28, 32, 33, 0, 32, 0, 1, 33, 25, 26, 33, 32, 25, 31, 25, 32, 2, 32, 1, 2, 3, 4, 29, 31, 2, 2, 4, 5, 29, 2, 5, 6, 29, 5, 30, 29, 6, 44, 30, 6, 18, 19, 52, 49, 56, 48, 34, 24, 25, 35, 23, 24, 35, 24, 34, 39, 22, 35, 22, 23, 35, 7, 44, 6, 8, 36, 44, 40, 36, 8, 8, 44, 7, 56, 21, 22, 55, 56, 22, 9, 40, 8, 20, 21, 56, 20, 56, 49, 9, 60, 40, 10, 60, 9, 20, 50, 19, 12, 10, 11, 13, 10, 12, 14, 60, 10, 13, 14, 10, 59, 60, 14, 58, 59, 14, 58, 14, 15, 16, 17, 60, 59, 16, 60, 15, 16, 59, 15, 59, 58, 20, 49, 50], "vertices": [2, 50, 41.97, -41.8, 0.94074, 66, 165.41, -22.6, 0.05926, 4, 48, 73.47, 27.54, 0.26795, 50, -5.75, -51.71, 0.4738, 49, 112.99, -11.41, 0.12255, 66, 143.5, -66.13, 0.1357, 4, 48, 38.23, 10.99, 0.6831, 50, -41.01, -35.22, 0.07866, 49, 92.73, -44.66, 0.04872, 66, 108.65, -83.49, 0.18952, 2, 48, 73.35, 10.89, 0.8455, 66, 143.77, -82.78, 0.1545, 2, 48, 58.59, -10.38, 0.91607, 66, 129.5, -104.39, 0.08393, 3, 46, 195.82, -119.82, 0.104, 47, 75.49, -4.55, 0.09191, 48, 14.36, -24.8, 0.80409, 4, 46, 178.62, -113.98, 0.19022, 47, 59.82, -13.72, 0.33409, 48, -2.7, -18.57, 0.46643, 66, 68.41, -113.98, 0.00926, 4, 46, 163.06, -108.69, 0.18724, 47, 45.64, -22.03, 0.3133, 48, -18.14, -12.93, 0.47469, 66, 52.84, -108.69, 0.02477, 2, 46, 151.52, -95.05, 0.91122, 66, 41.31, -95.05, 0.08878, 2, 46, 110.61, -87.69, 0.70564, 66, 0.4, -87.69, 0.29436, 2, 46, 81.05, -86.58, 0.63951, 66, -29.16, -86.58, 0.36049, 2, 46, 89.82, -114.32, 0.57, 66, -20.39, -114.32, 0.43, 2, 46, 68.72, -120.91, 0.57, 66, -41.49, -120.91, 0.43, 2, 46, 58.1, -115.9, 0.57, 66, -52.11, -115.9, 0.43, 2, 46, 51.03, -100.63, 0.64242, 66, -59.18, -100.63, 0.35758, 2, 46, 38.79, -106.76, 0.81659, 66, -71.43, -106.76, 0.18341, 2, 46, 2.68, -89.7, 0.77801, 66, -107.53, -89.7, 0.22199, 2, 46, -22.07, -19.3, 0.823, 66, -132.28, -19.3, 0.177, 2, 46, 1.2, 45.63, 0.51204, 66, -109.01, 45.63, 0.48796, 2, 46, 8.07, 64.81, 0.60869, 66, -102.14, 64.81, 0.39131, 2, 46, 35.44, 93.73, 0.80009, 66, -74.77, 93.73, 0.19991, 2, 46, 59.98, 119.66, 0.93554, 66, -50.23, 119.66, 0.06446, 2, 46, 109.26, 136.99, 0.99895, 66, -0.95, 136.99, 0.00105, 1, 46, 174.07, 135.27, 1, 3, 46, 205.59, 101.22, 0.80778, 49, -16.84, 104.63, 0.15658, 66, 95.38, 101.22, 0.03564, 3, 50, 58.94, 30.5, 0.43491, 49, 38.36, 61.89, 0.28116, 66, 119.35, 35.65, 0.28393, 2, 50, 75.56, 19.01, 0.92164, 66, 138.68, 41.52, 0.07836, 1, 50, 106.7, 26.9, 1, 1, 50, 83.79, -9.51, 1, 5, 47, 44.51, 27.24, 0.15139, 48, 19.12, 19.33, 0.44847, 50, -46.82, -15.19, 0.05757, 49, 72.19, -48.24, 0.1149, 66, 89.35, -75.58, 0.22767, 3, 47, 7.42, 19.08, 0.37772, 49, 34.32, -45.24, 0.09918, 66, 58.9, -52.89, 0.52311, 2, 49, 45.94, -9.07, 0.4826, 66, 87.99, -28.45, 0.5174, 2, 50, 20.62, -16.35, 0.7435, 66, 132.21, -23.49, 0.2565, 2, 50, 75.74, 0.94, 0.97172, 66, 152.95, 30.42, 0.02828, 4, 46, 200.45, 40.46, 0.18809, 50, 44.6, 56.29, 0.05831, 49, 11.15, 50.46, 0.14366, 66, 90.24, 40.46, 0.60994, 2, 46, 171.41, 90.12, 0.48644, 66, 61.2, 90.12, 0.51356, 2, 46, 164.84, -48.18, 0.43217, 66, 54.62, -48.18, 0.56783, 4, 46, 168.13, -6.02, 0.01949, 47, -28.65, 49.02, 0.02229, 49, 8.54, -6.09, 0.12791, 66, 57.92, -6.02, 0.83031, 2, 46, 167.84, 37.87, 0.15, 66, 57.63, 37.87, 0.85, 2, 46, 162.36, 71.5, 0.24107, 66, 52.15, 71.5, 0.75893, 2, 46, 163.11, -47.44, 0.41951, 66, 52.9, -47.44, 0.58049, 2, 46, 165.94, -5.87, 0.16355, 66, 55.73, -5.87, 0.83645, 2, 46, 165.14, 37.38, 0.15, 66, 54.93, 37.38, 0.85, 2, 46, 157.6, 71.4, 0.21735, 66, 47.39, 71.4, 0.78265, 3, 46, 163.5, -99.54, 0.61812, 47, 39.01, -15.71, 0.30445, 66, 53.29, -99.54, 0.07744, 2, 46, 45.38, 27.24, 0.16741, 66, -64.83, 27.24, 0.83259, 2, 46, 63.74, 44.98, 0.15, 66, -46.47, 44.98, 0.85, 2, 46, 70.7, 61.92, 0.22175, 66, -39.51, 61.92, 0.77825, 2, 46, 62.88, 78.71, 0.38, 66, -47.34, 78.71, 0.62, 2, 46, 46.53, 85.3, 0.51, 66, -63.68, 85.3, 0.49, 2, 46, 29.92, 79.34, 0.388, 66, -80.29, 79.34, 0.612, 2, 46, 15.08, 62.21, 0.38, 66, -95.13, 62.21, 0.62, 2, 46, 14.09, 45.32, 0.41, 66, -96.12, 45.32, 0.59, 2, 46, 24.3, 27.06, 0.192, 66, -85.91, 27.06, 0.808, 1, 66, -61.57, 15.3, 1, 2, 46, 84.87, 62.14, 0.16757, 66, -25.34, 62.14, 0.83243, 2, 46, 61.9, 94.84, 0.68145, 66, -48.31, 94.84, 0.31855, 2, 46, 22.54, 21.88, 0.16, 66, -87.67, 21.88, 0.84, 2, 46, 43.15, -95.95, 0.73445, 66, -67.06, -95.95, 0.26555, 2, 46, 41.77, -87.24, 0.67858, 66, -68.44, -87.24, 0.32142, 2, 46, 60.05, -70.36, 0.50195, 66, -50.16, -70.36, 0.49805, 2, 46, 48.49, 51.09, 0.25, 66, -61.72, 51.09, 0.75, 2, 46, 48.17, 73.71, 0.15634, 66, -62.04, 73.71, 0.84366], "hull": 29, "edges": [10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 56, 54, 56, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 42, 44, 32, 34, 4, 58, 58, 60, 62, 64, 64, 66, 66, 54, 50, 68, 68, 70, 70, 44, 60, 72, 62, 74, 72, 74, 74, 76, 76, 78, 78, 44, 16, 80, 80, 82, 82, 84, 84, 86, 86, 44, 14, 88, 88, 72, 14, 16, 10, 12, 12, 14, 12, 60, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 90, 108, 110, 110, 112, 38, 40, 40, 42, 112, 40, 34, 36, 36, 38, 36, 114, 114, 108, 30, 32, 30, 28, 24, 26, 28, 26, 22, 24, 22, 20, 20, 18, 18, 16, 28, 116, 116, 118, 118, 120, 120, 20], "width": 271, "height": 298}}, "head-bb": {"head": {"type": "boundingbox", "vertexCount": 6, "vertices": [-19.14, -70.3, 40.8, -118.08, 257.78, -115.62, 285.17, 57.18, 120.77, 164.95, -5.07, 76.95]}}, "hoverboard-board": {"hoverboard-board": {"type": "mesh", "uvs": [0.13865, 0.56624, 0.11428, 0.51461, 0.07619, 0.52107, 0.02364, 0.52998, 0.01281, 0.53182, 0, 0.37979, 0, 0.2206, 0.00519, 0.10825, 0.01038, 0.10726, 0.03834, 0.10194, 0.05091, 0, 0.08326, 0, 0.10933, 0.04206, 0.1382, 0.08865, 0.18916, 0.24067, 0.22234, 0.4063, 0.23886, 0.44063, 0.83412, 0.44034, 0.88444, 0.38296, 0.92591, 0.32639, 0.95996, 0.28841, 0.98612, 0.28542, 1, 0.38675, 0.99494, 0.47104, 0.97883, 0.53251, 0.94409, 0.62135, 0.90206, 0.69492, 0.86569, 0.71094, 0.82822, 0.70791, 0.81286, 0.77127, 0.62931, 0.77266, 0.61364, 0.70645, 0.47166, 0.70664, 0.45901, 0.77827, 0.27747, 0.76986, 0.2658, 0.70372, 0.24976, 0.71381, 0.24601, 0.77827, 0.23042, 0.84931, 0.20926, 0.90956, 0.17299, 1, 0.15077, 0.99967, 0.12906, 0.90192, 0.10369, 0.73693, 0.10198, 0.62482, 0.09131, 0.47272, 0.09133, 0.41325, 0.15082, 0.41868, 0.21991, 0.51856, 0.06331, 0.10816, 0.08383, 0.21696, 0.08905, 0.37532, 0.15903, 0.58726, 0.17538, 0.65706, 0.20118, 0.8029, 0.17918, 0.55644, 0.22166, 0.5802, 0.86259, 0.57962, 0.92346, 0.48534, 0.96691, 0.36881, 0.0945, 0.13259, 0.12688, 0.17831, 0.15986, 0.24682, 0.18036, 0.31268, 0.20607, 0.4235, 0.16074, 0.85403, 0.13624, 0.70122, 0.12096, 0.64049, 0.02396, 0.21811, 0.02732, 0.37839, 0.02557, 0.4972, 0.14476, 0.45736, 0.18019, 0.51689, 0.19692, 0.56636], "triangles": [10, 11, 12, 9, 10, 12, 49, 9, 12, 60, 49, 12, 13, 60, 12, 61, 60, 13, 50, 49, 60, 50, 60, 61, 68, 8, 9, 68, 9, 49, 68, 49, 50, 7, 8, 68, 6, 7, 68, 61, 13, 14, 62, 61, 14, 50, 61, 62, 63, 62, 14, 59, 20, 21, 19, 20, 59, 51, 50, 62, 51, 62, 63, 51, 69, 68, 51, 68, 50, 6, 68, 69, 5, 6, 69, 18, 19, 59, 15, 63, 14, 59, 21, 22, 47, 51, 63, 47, 46, 51, 47, 63, 64, 15, 64, 63, 64, 15, 16, 71, 46, 47, 23, 59, 22, 69, 51, 70, 45, 46, 71, 70, 51, 2, 58, 18, 59, 58, 59, 23, 17, 18, 58, 70, 5, 69, 2, 51, 46, 1, 45, 71, 47, 48, 71, 47, 64, 48, 48, 72, 71, 1, 71, 72, 16, 48, 64, 45, 2, 46, 2, 45, 1, 70, 4, 5, 3, 70, 2, 3, 4, 70, 24, 58, 23, 72, 0, 1, 73, 55, 72, 55, 0, 72, 48, 73, 72, 57, 17, 58, 25, 57, 58, 56, 48, 16, 73, 48, 56, 56, 16, 17, 56, 17, 57, 52, 0, 55, 24, 25, 58, 44, 0, 52, 67, 44, 52, 52, 56, 53, 73, 52, 55, 56, 52, 73, 67, 52, 53, 26, 57, 25, 66, 67, 53, 56, 32, 35, 53, 56, 35, 56, 57, 32, 28, 31, 57, 57, 31, 32, 57, 27, 28, 26, 27, 57, 36, 53, 35, 43, 44, 67, 43, 67, 66, 34, 35, 32, 29, 31, 28, 30, 31, 29, 53, 54, 66, 53, 36, 54, 33, 34, 32, 37, 54, 36, 65, 43, 66, 38, 54, 37, 54, 65, 66, 39, 65, 54, 42, 43, 65, 38, 39, 54, 40, 42, 65, 40, 41, 42, 65, 39, 40], "vertices": [-189.36, 15.62, -201.35, 23.47, -220.09, 22.49, -245.95, 21.13, -251.28, 20.86, -257.58, 43.96, -257.57, 68.16, -255.02, 85.24, -252.47, 85.39, -238.71, 86.2, -232.52, 101.69, -216.61, 101.69, -203.78, 95.3, -189.58, 88.21, -164.51, 65.1, -148.19, 39.93, -140.06, 34.71, 152.82, 34.73, 177.57, 43.45, 197.97, 52.05, 214.72, 57.82, 227.6, 58.27, 234.42, 42.87, 231.94, 30.06, 224.01, 20.72, 206.91, 7.21, 186.23, -3.97, 168.34, -6.4, 149.9, -5.94, 142.35, -15.57, 52.04, -15.77, 44.33, -5.71, -25.52, -5.73, -31.75, -16.62, -121.07, -15.34, -126.81, -5.28, -134.7, -6.81, -136.54, -16.61, -144.22, -27.41, -154.63, -36.57, -172.47, -50.31, -183.41, -50.26, -194.09, -35.4, -206.56, -10.32, -207.4, 6.72, -212.65, 29.84, -212.64, 38.88, -183.37, 38.05, -149.38, 22.86, -226.43, 85.25, -216.33, 68.71, -213.76, 44.64, -179.34, 12.42, -171.29, 1.81, -158.6, -20.36, -169.42, 17.11, -148.52, 13.49, 166.82, 13.56, 196.76, 27.89, 218.14, 45.6, -211.08, 81.54, -195.15, 74.59, -178.93, 64.17, -168.84, 54.16, -156.19, 37.31, -178.5, -28.13, -190.55, -4.9, -198.07, 4.33, -245.79, 68.54, -244.14, 44.18, -245, 26.12, -186.36, 32.17, -168.92, 23.12, -160.69, 15.6], "hull": 45, "edges": [0, 2, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88, 2, 90, 90, 92, 92, 94, 94, 96, 96, 32, 18, 98, 98, 100, 100, 102, 2, 4, 102, 4, 92, 102, 0, 104, 104, 106, 106, 108, 78, 80, 108, 78, 74, 76, 76, 78, 62, 56, 64, 70, 0, 110, 112, 114, 114, 116, 116, 118, 118, 42, 50, 116, 114, 34, 98, 120, 120, 122, 22, 24, 24, 26, 120, 24, 122, 124, 124, 126, 126, 128, 128, 96, 80, 130, 130, 132, 132, 134, 134, 88, 14, 16, 16, 18, 136, 16, 136, 138, 138, 140, 4, 6, 6, 8, 140, 6, 96, 112, 92, 142, 142, 144, 110, 146, 146, 112, 144, 146], "width": 492, "height": 152}}, "hoverboard-thruster-front": {"hoverboard-thruster": {"x": 0.02, "y": -7.08, "rotation": 0.17, "width": 60, "height": 64}}, "hoverboard-thruster-rear": {"hoverboard-thruster": {"x": 1.1, "y": -6.29, "rotation": 0.17, "width": 60, "height": 64}}, "hoverglow-front": {"hoverglow-small": {"x": 2.13, "y": -2, "scaleX": 0.303, "scaleY": 0.495, "rotation": 0.15, "width": 274, "height": 75}}, "hoverglow-rear": {"hoverglow-small": {"x": 1.39, "y": -2.09, "scaleX": 0.303, "scaleY": 0.495, "rotation": 0.61, "width": 274, "height": 75}}, "mouth": {"mouth-grind": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 66, -98.93, -85.88, 0.22, 46, 11.28, -85.88, 0.78, 2, 66, -129.77, 1.84, 0.6, 46, -19.56, 1.84, 0.4, 2, 66, -74.12, 21.41, 0.6, 46, 36.09, 21.41, 0.4, 2, 66, -43.28, -66.32, 0.4, 46, 66.93, -66.32, 0.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 59}, "mouth-oooo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 46, 11.28, -85.89, 0.22, 66, -98.93, -85.89, 0.78, 2, 46, -19.56, 1.85, 0.6, 66, -129.78, 1.85, 0.4, 2, 46, 36.1, 21.42, 0.6, 66, -74.12, 21.42, 0.4, 2, 46, 66.94, -66.32, 0.4, 66, -43.27, -66.32, 0.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 59}, "mouth-smile": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 66, -98.93, -85.89, 0.21075, 46, 11.28, -85.89, 0.78925, 2, 66, -129.77, 1.85, 0.6, 46, -19.56, 1.85, 0.4, 2, 66, -74.11, 21.42, 0.6, 46, 36.1, 21.42, 0.4, 2, 66, -43.27, -66.32, 0.40772, 46, 66.94, -66.32, 0.59228], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 59}}, "muzzle": {"muzzle01": {"x": 151.97, "y": 5.81, "scaleX": 3.7361, "scaleY": 3.7361, "rotation": 0.15, "width": 133, "height": 79}, "muzzle02": {"x": 187.25, "y": 5.9, "scaleX": 4.0623, "scaleY": 4.0623, "rotation": 0.15, "width": 135, "height": 84}, "muzzle03": {"x": 231.96, "y": 6.02, "scaleX": 4.1325, "scaleY": 4.1325, "rotation": 0.15, "width": 166, "height": 106}, "muzzle04": {"x": 231.96, "y": 6.02, "scaleX": 4.0046, "scaleY": 4.0046, "rotation": 0.15, "width": 149, "height": 90}, "muzzle05": {"x": 293.8, "y": 6.19, "scaleX": 4.4673, "scaleY": 4.4673, "rotation": 0.15, "width": 135, "height": 75}}, "muzzle-glow": {"muzzle-glow": {"width": 50, "height": 50}}, "muzzle-ring": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring2": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring3": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring4": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "neck": {"neck": {"x": 9.77, "y": -3.01, "rotation": -55.22, "width": 36, "height": 41}}, "portal-bg": {"portal-bg": {"x": -3.1, "y": 7.25, "scaleX": 1.0492, "scaleY": 1.0492, "width": 266, "height": 266}}, "portal-flare1": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare2": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare3": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare4": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare5": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare6": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare7": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare8": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare9": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare10": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-shade": {"portal-shade": {"width": 266, "height": 266}}, "portal-streaks1": {"portal-streaks1": {"scaleX": 0.9774, "scaleY": 0.9774, "width": 252, "height": 256}}, "portal-streaks2": {"portal-streaks2": {"x": -1.64, "y": 2.79, "width": 250, "height": 249}}, "rear-bracer": {"rear-bracer": {"x": 11.15, "y": -2.2, "rotation": 66.17, "width": 56, "height": 72}}, "rear-foot": {"rear-foot": {"type": "mesh", "uvs": [0.48368, 0.1387, 0.51991, 0.21424, 0.551, 0.27907, 0.58838, 0.29816, 0.63489, 0.32191, 0.77342, 0.39267, 1, 0.73347, 1, 1, 0.54831, 0.99883, 0.31161, 1, 0, 1, 0, 0.41397, 0.13631, 0, 0.41717, 0], "triangles": [8, 3, 4, 8, 4, 5, 8, 5, 6, 8, 6, 7, 11, 1, 10, 3, 9, 2, 2, 10, 1, 12, 13, 0, 0, 11, 12, 1, 11, 0, 2, 9, 10, 3, 8, 9], "vertices": [2, 8, 10.45, 29.41, 0.90802, 9, -6.74, 49.62, 0.09198, 2, 8, 16.56, 29.27, 0.84259, 9, -2.65, 45.09, 0.15741, 2, 8, 21.8, 29.15, 0.69807, 9, 0.85, 41.2, 0.30193, 2, 8, 25.53, 31.43, 0.52955, 9, 5.08, 40.05, 0.47045, 2, 8, 30.18, 34.27, 0.39303, 9, 10.33, 38.62, 0.60697, 2, 8, 44.02, 42.73, 0.27525, 9, 25.98, 34.36, 0.72475, 2, 8, 76.47, 47.28, 0.21597, 9, 51.56, 13.9, 0.78403, 2, 8, 88.09, 36.29, 0.28719, 9, 51.55, -2.09, 0.71281, 2, 8, 52.94, -0.73, 0.47576, 9, 0.52, -1.98, 0.52424, 2, 8, 34.63, -20.23, 0.68757, 9, -26.23, -2.03, 0.31243, 2, 8, 10.44, -45.81, 0.84141, 9, -61.43, -2, 0.15859, 2, 8, -15.11, -21.64, 0.93283, 9, -61.4, 33.15, 0.06717, 1, 8, -22.57, 6.61, 1, 1, 8, -0.76, 29.67, 1], "hull": 14, "edges": [14, 12, 10, 12, 14, 16, 16, 18, 18, 20, 4, 18, 20, 22, 24, 26, 22, 24, 4, 2, 2, 20, 4, 6, 6, 16, 6, 8, 8, 10, 2, 0, 0, 26], "width": 113, "height": 60}}, "rear-shin": {"rear-shin": {"x": 58.29, "y": -2.75, "rotation": 92.37, "width": 75, "height": 178}}, "rear-thigh": {"rear-thigh": {"x": 33.11, "y": -4.11, "rotation": 72.54, "width": 55, "height": 94}}, "rear-upper-arm": {"rear-upper-arm": {"x": 21.13, "y": 4.09, "rotation": 89.33, "width": 40, "height": 87}}, "side-glow1": {"hoverglow-small": {"x": 2.09, "scaleX": 0.2353, "scaleY": 0.4132, "width": 274, "height": 75}}, "side-glow2": {"hoverglow-small": {"x": 2.09, "scaleX": 0.2353, "scaleY": 0.4132, "width": 274, "height": 75}}, "side-glow3": {"hoverglow-small": {"x": 2.09, "scaleX": 0.3586, "scaleY": 0.6297, "width": 274, "height": 75}}, "torso": {"torso": {"type": "mesh", "uvs": [0.6251, 0.12672, 1, 0.26361, 1, 0.28871, 1, 0.66021, 1, 0.68245, 0.92324, 0.69259, 0.95116, 0.84965, 0.77124, 1, 0.49655, 1, 0.27181, 1, 0.13842, 0.77196, 0.09886, 0.6817, 0.05635, 0.58471, 0, 0.45614, 0, 0.33778, 0, 0.19436, 0.14463, 0, 0.27802, 0, 0.72525, 0.27835, 0.76091, 0.46216, 0.84888, 0.67963, 0.68257, 0.63249, 0.53986, 0.3847, 0.25443, 0.3217, 0.30063, 0.55174, 0.39553, 0.79507, 0.26389, 0.17007, 0.5241, 0.18674, 0.71492, 0.76655, 0.82151, 0.72956, 0.27626, 0.4304, 0.62327, 0.52952, 0.3455, 0.66679, 0.53243, 0.2914], "triangles": [18, 1, 2, 19, 2, 3, 18, 0, 1, 23, 15, 26, 27, 26, 16, 14, 15, 23, 15, 16, 26, 17, 27, 16, 13, 14, 23, 0, 27, 17, 13, 23, 30, 11, 12, 24, 21, 31, 19, 12, 13, 30, 24, 22, 31, 31, 22, 19, 12, 30, 24, 32, 24, 31, 24, 30, 22, 3, 20, 19, 32, 31, 21, 11, 24, 32, 4, 5, 3, 8, 28, 7, 7, 29, 6, 7, 28, 29, 9, 25, 8, 8, 25, 28, 9, 10, 25, 29, 5, 6, 10, 32, 25, 25, 21, 28, 25, 32, 21, 10, 11, 32, 28, 21, 29, 29, 20, 5, 29, 21, 20, 5, 20, 3, 20, 21, 19, 33, 26, 27, 22, 18, 19, 19, 18, 2, 33, 27, 18, 30, 23, 22, 22, 33, 18, 23, 33, 22, 33, 23, 26, 27, 0, 18], "vertices": [2, 29, 44.59, -10.39, 0.88, 40, -17.65, 33.99, 0.12, 3, 28, 59.65, -45.08, 0.12189, 29, 17.13, -45.08, 0.26811, 40, 22.68, 15.82, 0.61, 3, 28, 55.15, -44.72, 0.1345, 29, 12.63, -44.72, 0.2555, 40, 23.43, 11.37, 0.61, 3, 27, 31.01, -39.45, 0.51133, 28, -11.51, -39.45, 0.30867, 40, 34.58, -54.57, 0.18, 3, 27, 27.01, -39.14, 0.53492, 28, -15.5, -39.14, 0.28508, 40, 35.25, -58.52, 0.18, 2, 27, 25.79, -31.5, 0.75532, 28, -16.73, -31.5, 0.24468, 1, 27, -2.61, -32, 1, 1, 27, -28.2, -12.29, 1, 1, 27, -26.08, 14.55, 1, 1, 27, -24.35, 36.5, 1, 2, 27, 17.6, 46.3, 0.8332, 28, -24.92, 46.3, 0.1668, 2, 27, 34.1, 48.89, 0.59943, 28, -8.42, 48.89, 0.40058, 3, 27, 51.83, 51.67, 0.29262, 28, 9.32, 51.67, 0.63181, 29, -33.2, 51.67, 0.07557, 3, 27, 75.34, 55.35, 0.06656, 28, 32.82, 55.35, 0.62298, 29, -9.7, 55.35, 0.31046, 2, 28, 54.06, 53.67, 0.37296, 29, 11.54, 53.67, 0.62704, 2, 28, 79.79, 51.64, 0.10373, 29, 37.27, 51.64, 0.89627, 1, 29, 71.04, 34.76, 1, 1, 29, 70.01, 21.72, 1, 1, 30, 36.74, 7.06, 1, 3, 30, 45.7, -24.98, 0.67, 28, 25.87, -18.9, 0.3012, 29, -16.65, -18.9, 0.0288, 2, 27, 28.69, -24.42, 0.77602, 28, -13.83, -24.42, 0.22398, 3, 30, 43.24, -56.49, 0.064, 27, 38.43, -8.84, 0.67897, 28, -4.09, -8.84, 0.25703, 3, 30, 22.02, -14.85, 0.29, 28, 41.48, 1.59, 0.53368, 29, -1.04, 1.59, 0.17632, 3, 30, -7.45, -8.33, 0.76, 28, 54.98, 28.59, 0.06693, 29, 12.46, 28.59, 0.17307, 3, 30, 3.91, -48.4, 0.25, 27, 55.87, 27.33, 0.15843, 28, 13.35, 27.33, 0.59157, 1, 27, 11.47, 21.51, 1, 2, 30, -11.09, 18.74, 0.416, 29, 39.6, 25.51, 0.584, 2, 30, 14.56, 20.03, 0.53, 29, 34.6, 0.33, 0.47, 1, 27, 14.12, -10.1, 1, 2, 27, 19.94, -21.03, 0.92029, 28, -22.58, -21.03, 0.07971, 3, 30, -2.08, -27.26, 0.29, 28, 35.31, 27.99, 0.49582, 29, -7.21, 27.99, 0.21418, 2, 30, 34.42, -39.19, 0.25, 28, 14.84, -4.5, 0.75, 2, 27, 34.87, 24.58, 0.67349, 28, -7.64, 24.58, 0.32651, 2, 30, 18.5, 1.59, 0.76, 29, 15.76, 1, 0.24], "hull": 18, "edges": [14, 12, 12, 10, 10, 8, 18, 20, 32, 34, 30, 32, 2, 4, 36, 4, 36, 38, 38, 40, 4, 6, 6, 8, 40, 6, 40, 42, 14, 16, 16, 18, 50, 16, 46, 52, 54, 36, 2, 0, 0, 34, 54, 0, 54, 32, 20, 50, 14, 56, 56, 42, 50, 56, 56, 58, 58, 40, 58, 10, 46, 60, 60, 48, 26, 60, 60, 44, 24, 26, 24, 48, 42, 62, 62, 44, 48, 62, 48, 64, 64, 50, 42, 64, 20, 22, 22, 24, 64, 22, 26, 28, 28, 30, 28, 46, 44, 66, 66, 54, 46, 66, 66, 36, 62, 38], "width": 98, "height": 180}}}}], "events": {"footstep": {}}, "animations": {"aim": {"slots": {"crosshair": {"attachment": [{"name": "crosshair"}]}}, "bones": {"front-fist": {"rotate": [{"value": 36.08}]}, "rear-bracer": {"rotate": [{"value": -26.55}]}, "rear-upper-arm": {"rotate": [{"value": 62.31}]}, "front-bracer": {"rotate": [{"value": 9.11}]}, "gun": {"rotate": [{"value": -0.31}]}}, "ik": {"aim-ik": [{"mix": 0.995}]}, "transform": {"aim-front-arm-transform": [{"mixRotate": 0.784, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "aim-head-transform": [{"mixRotate": 0.659, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "aim-torso-transform": [{"mixRotate": 0.423, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}}, "death": {"slots": {"eye": {"attachment": [{"name": "eye-surprised"}, {"time": 0.5333, "name": "eye-indifferent"}, {"time": 2.2, "name": "eye-surprised"}, {"time": 4.6, "name": "eye-indifferent"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "mouth": {"attachment": [{"name": "mouth-oooo"}, {"time": 0.5333, "name": "mouth-grind"}, {"time": 1.4, "name": "mouth-oooo"}, {"time": 2.1667, "name": "mouth-grind"}, {"time": 4.5333, "name": "mouth-oooo"}]}}, "bones": {"head": {"rotate": [{"value": -2.83, "curve": [0.015, -2.83, 0.036, 12.72]}, {"time": 0.0667, "value": 12.19, "curve": [0.096, 11.68, 0.119, -1.14]}, {"time": 0.1333, "value": -6.86, "curve": [0.149, -13.27, 0.21, -37.28]}, {"time": 0.3, "value": -36.86, "curve": [0.354, -36.61, 0.412, -32.35]}, {"time": 0.4667, "value": -23.49, "curve": [0.49, -19.87, 0.512, -3.29]}, {"time": 0.5333, "value": -3.24, "curve": [0.56, -3.39, 0.614, -67.25]}, {"time": 0.6333, "value": -74.4, "curve": [0.652, -81.58, 0.702, -88.94]}, {"time": 0.7333, "value": -88.93, "curve": [0.805, -88.91, 0.838, -80.87]}, {"time": 0.8667, "value": -81.03, "curve": [0.922, -81.32, 0.976, -85.29]}, {"time": 1, "value": -85.29, "curve": "stepped"}, {"time": 2.2333, "value": -85.29, "curve": [2.314, -85.29, 2.382, -68.06]}, {"time": 2.4667, "value": -63.48, "curve": [2.57, -57.87, 2.916, -55.24]}, {"time": 3.2, "value": -55.1, "curve": [3.447, -54.98, 4.135, -56.61]}, {"time": 4.2667, "value": -58.23, "curve": [4.672, -63.24, 4.646, -82.69]}, {"time": 4.9333, "value": -85.29}], "scale": [{"time": 0.4667, "curve": [0.469, 1.005, 0.492, 1.065, 0.475, 1.018, 0.492, 0.94]}, {"time": 0.5, "x": 1.065, "y": 0.94, "curve": [0.517, 1.065, 0.541, 0.991, 0.517, 0.94, 0.542, 1.026]}, {"time": 0.5667, "x": 0.99, "y": 1.025, "curve": [0.593, 0.988, 0.609, 1.002, 0.595, 1.024, 0.607, 1.001]}, {"time": 0.6333}]}, "neck": {"rotate": [{"value": -2.83, "curve": [0.114, 1.33, 0.195, 4.13]}, {"time": 0.2667, "value": 4.13, "curve": [0.351, 4.14, 0.444, -24.5]}, {"time": 0.5, "value": -24.69, "curve": [0.571, -23.89, 0.55, 34.22]}, {"time": 0.6667, "value": 35.13, "curve": [0.713, 34.81, 0.756, 22.76]}, {"time": 0.8333, "value": 22.82, "curve": [0.868, 22.84, 0.916, 47.95]}, {"time": 0.9667, "value": 47.95, "curve": "stepped"}, {"time": 2.2333, "value": 47.95, "curve": [2.3, 47.95, 2.617, 18.72]}, {"time": 2.6667, "value": 18.51, "curve": [3.172, 16.58, 4.06, 16.79]}, {"time": 4.5333, "value": 18.51, "curve": [4.707, 19.13, 4.776, 41.11]}, {"time": 4.8, "value": 47.95}]}, "torso": {"rotate": [{"value": -8.62, "curve": [0.01, -16.71, 0.032, -33.6]}, {"time": 0.0667, "value": -33.37, "curve": [0.182, -32.61, 0.298, 123.07]}, {"time": 0.4667, "value": 122.77, "curve": [0.511, 122.69, 0.52, 100.2]}, {"time": 0.5667, "value": 88.96, "curve": [0.588, 83.89, 0.667, 75.34]}, {"time": 0.7, "value": 75.34, "curve": [0.767, 75.34, 0.9, 76.03]}, {"time": 0.9667, "value": 76.03}]}, "front-upper-arm": {"rotate": [{"value": -38.86, "curve": [0.022, -40.38, 0.096, -41.92]}, {"time": 0.1333, "value": -41.92, "curve": [0.176, -41.92, 0.216, -16.92]}, {"time": 0.2333, "value": -4.35, "curve": [0.258, 13.69, 0.308, 60.35]}, {"time": 0.4, "value": 60.17, "curve": [0.496, 59.98, 0.539, 33.63]}, {"time": 0.5667, "value": 23.06, "curve": [0.595, 32.71, 0.675, 53.71]}, {"time": 0.7333, "value": 53.61, "curve": [0.797, 53.51, 0.926, 30.98]}, {"time": 0.9333, "value": 19.57, "curve": "stepped"}, {"time": 1.9667, "value": 19.57, "curve": [2.245, 19.57, 2.702, 77.03]}, {"time": 3.0667, "value": 77.06, "curve": [3.209, 77.33, 3.291, 67.99]}, {"time": 3.4333, "value": 67.96, "curve": [3.608, 68.34, 3.729, 73.88]}, {"time": 3.8333, "value": 73.42, "curve": [4.152, 73.91, 4.46, 71.98]}, {"time": 4.6333, "value": 64.77, "curve": [4.688, 62.5, 4.847, 26.42]}, {"time": 4.8667, "value": 10.94}]}, "rear-upper-arm": {"rotate": [{"value": -44.7, "curve": [0.033, -44.7, 0.12, 54.89]}, {"time": 0.1333, "value": 64.62, "curve": [0.154, 79.18, 0.214, 79.42]}, {"time": 0.2667, "value": 63.4, "curve": [0.293, 55.19, 0.332, 30.13]}, {"time": 0.3667, "value": 30.13, "curve": [0.4, 30.13, 0.441, 39.87]}, {"time": 0.4667, "value": 55.13, "curve": [0.488, 68.18, 0.52, 100.72]}, {"time": 0.5333, "value": 111.96, "curve": [0.551, 126.88, 0.627, 185.97]}, {"time": 0.6667, "value": 185.97, "curve": [0.692, 185.97, 0.736, 162.43]}, {"time": 0.8, "value": 158.01, "curve": [0.9, 151.12, 1.017, 144.01]}, {"time": 1.1, "value": 144.01, "curve": "stepped"}, {"time": 2.3667, "value": 144.01, "curve": [2.492, 144.01, 2.742, 138.63]}, {"time": 2.8667, "value": 138.63, "curve": [3.067, 138.63, 3.467, 138.63]}, {"time": 3.6667, "value": 138.63, "curve": [3.883, 138.63, 4.317, 135.18]}, {"time": 4.5333, "value": 135.18, "curve": [4.575, 135.18, 4.692, 131.59]}, {"time": 4.7333, "value": 131.59, "curve": [4.758, 131.59, 4.517, 144.01]}, {"time": 4.8333, "value": 144.01}], "translate": [{"time": 0.4667, "curve": [0.517, 0, 0.617, -34.96, 0.517, 0, 0.617, -16.59]}, {"time": 0.6667, "x": -35.02, "y": -16.62}]}, "front-bracer": {"rotate": [{"value": 21.88, "curve": [0.033, 21.88, 0.099, 20.44]}, {"time": 0.1333, "value": 9.43, "curve": [0.164, -0.29, 0.162, -38.26]}, {"time": 0.2, "value": -38.05, "curve": [0.24, -37.96, 0.228, -17.82]}, {"time": 0.3333, "value": -9.73, "curve": [0.372, -6.76, 0.431, -0.74]}, {"time": 0.4667, "value": 6.47, "curve": [0.489, 11.05, 0.503, 19.09]}, {"time": 0.5333, "value": 19.09, "curve": [0.571, 19.09, 0.554, -42.67]}, {"time": 0.6, "value": -42.67, "curve": [0.653, -42.67, 0.691, -13.8]}, {"time": 0.7, "value": -3.54, "curve": [0.707, 3.8, 0.719, 24.94]}, {"time": 0.8, "value": 25.31, "curve": [0.902, 24.75, 0.992, -0.34]}, {"time": 1, "value": -32.16, "curve": "stepped"}, {"time": 2.2333, "value": -32.16, "curve": [2.6, -32.16, 2.638, -5.3]}, {"time": 2.7, "value": -1.96, "curve": [2.707, -1.56, 2.775, 1.67]}, {"time": 2.8, "value": 1.67, "curve": [2.825, 1.67, 2.875, -0.39]}, {"time": 2.9, "value": -0.39, "curve": [2.925, -0.39, 2.975, 0.26]}, {"time": 3, "value": 0.26, "curve": [3.025, 0.26, 3.075, -1.81]}, {"time": 3.1, "value": -1.81, "curve": [3.125, -1.81, 3.175, -0.52]}, {"time": 3.2, "value": -0.52, "curve": [3.225, -0.52, 3.275, -2.41]}, {"time": 3.3, "value": -2.41, "curve": [3.333, -2.41, 3.4, -0.38]}, {"time": 3.4333, "value": -0.38, "curve": [3.467, -0.38, 3.533, -2.25]}, {"time": 3.5667, "value": -2.25, "curve": [3.592, -2.25, 3.642, -0.33]}, {"time": 3.6667, "value": -0.33, "curve": [3.7, -0.33, 3.767, -1.34]}, {"time": 3.8, "value": -1.34, "curve": [3.825, -1.34, 3.862, -0.77]}, {"time": 3.9, "value": -0.77, "curve": [3.942, -0.77, 3.991, -1.48]}, {"time": 4, "value": -1.87, "curve": [4.167, -1.87, 4.5, -1.96]}, {"time": 4.6667, "value": -1.96, "curve": [4.709, 18.05, 4.767, 34.55]}, {"time": 4.8, "value": 34.55, "curve": [4.84, 34.24, 4.902, 12.03]}, {"time": 4.9333, "value": -18.75}]}, "front-fist": {"rotate": [{"value": -2.33, "curve": [0.019, 4.43, 0.069, 10.82]}, {"time": 0.1, "value": 10.6, "curve": [0.148, 10.6, 0.123, -15.24]}, {"time": 0.2, "value": -15.35, "curve": [0.266, -15.44, 0.316, -6.48]}, {"time": 0.3333, "value": -3.9, "curve": [0.362, 0.43, 0.479, 22.36]}, {"time": 0.5667, "value": 22.01, "curve": [0.61, 21.84, 0.627, 12.85]}, {"time": 0.6333, "value": 9.05, "curve": [0.643, 2.77, 0.622, -39.43]}, {"time": 0.7, "value": -39.5, "curve": [0.773, -39.57, 0.814, 14.77]}, {"time": 0.8667, "value": 14.81, "curve": [0.965, 14.88, 1.1, 5.64]}, {"time": 1.1, "value": -6.08, "curve": "stepped"}, {"time": 2.2333, "value": -6.08, "curve": [2.307, -6.08, 2.427, -25.89]}, {"time": 2.5333, "value": -22.42, "curve": [2.598, -20.38, 2.657, 5.73]}, {"time": 2.7, "value": 5.73, "curve": [2.77, 5.73, 2.851, -5.38]}, {"time": 2.9333, "value": -5.38, "curve": [3.008, -5.38, 3.087, -4.54]}, {"time": 3.1667, "value": -4.17, "curve": [3.223, -3.91, 4.486, 5.73]}, {"time": 4.6667, "value": 5.73, "curve": [4.733, 5.73, 4.886, -2.47]}, {"time": 4.9333, "value": -6.52}]}, "rear-bracer": {"rotate": [{"value": 10.36, "curve": [0.033, 10.36, 0.1, -32.89]}, {"time": 0.1333, "value": -32.89, "curve": [0.183, -32.89, 0.283, -4.45]}, {"time": 0.3333, "value": -4.45, "curve": [0.367, -4.45, 0.438, -6.86]}, {"time": 0.4667, "value": -8.99, "curve": [0.529, -13.62, 0.605, -20.58]}, {"time": 0.6333, "value": -23.2, "curve": [0.708, -30.18, 0.758, -35.56]}, {"time": 0.8, "value": -35.56, "curve": [0.875, -35.56, 1.025, -23.2]}, {"time": 1.1, "value": -23.2}]}, "gun": {"rotate": [{"value": -2.79, "curve": [0.033, -2.79, 0.12, -7.22]}, {"time": 0.1333, "value": -8.52, "curve": [0.168, -11.87, 0.29, -23.71]}, {"time": 0.3333, "value": -26.24, "curve": [0.369, -28.31, 0.436, -29.75]}, {"time": 0.5, "value": -29.66, "curve": [0.552, -29.58, 0.611, -25.47]}, {"time": 0.6333, "value": -22.68, "curve": [0.656, -19.76, 0.68, -10.02]}, {"time": 0.7, "value": -6.49, "curve": [0.722, -2.6, 0.75, -1.22]}, {"time": 0.7667, "value": -1.35, "curve": [0.792, -1.55, 0.842, -19.74]}, {"time": 0.8667, "value": -19.8}]}, "hip": {"translate": [{"curve": [0.098, -42.62, 0.166, -79.85, 0.029, 84.97, 0.109, 155.93]}, {"time": 0.2667, "x": -133.79, "y": 152.44, "curve": [0.361, -184.63, 0.392, -203.69, 0.42, 149.12, 0.467, -15.7]}, {"time": 0.4667, "x": -230.02, "y": -113.87, "curve": [0.523, -249.86, 0.565, -261.7, 0.473, -133.1, 0.583, -203.43]}, {"time": 0.6, "x": -268.57, "y": -203.43, "curve": [0.663, -280.98, 0.816, -290.05, 0.708, -203.43, 0.892, -203.5]}, {"time": 1, "x": -290.42, "y": -203.5}]}, "front-thigh": {"rotate": [{"curve": [0.06, 1.02, 0.151, 45.23]}, {"time": 0.1667, "value": 54.01, "curve": [0.19, 66.85, 0.358, 169.85]}, {"time": 0.5, "value": 169.51, "curve": [0.628, 169.85, 0.692, 108.85]}, {"time": 0.7, "value": 97.74, "curve": [0.723, 102.6, 0.805, 111.6]}, {"time": 0.8667, "value": 111.69, "curve": [0.899, 111.83, 1.015, 109.15]}, {"time": 1.0667, "value": 95.8}]}, "front-shin": {"rotate": [{"curve": [0.086, -0.02, 0.191, -24.25]}, {"time": 0.2, "value": -26.5, "curve": [0.214, -29.92, 0.249, -40.51]}, {"time": 0.3333, "value": -40.57, "curve": [0.431, -40.7, 0.459, -11.34]}, {"time": 0.4667, "value": -8.71, "curve": [0.477, -5.16, 0.524, 17.13]}, {"time": 0.6, "value": 16.98, "curve": [0.632, 17.09, 0.625, 2.76]}, {"time": 0.6333, "value": 2.76, "curve": [0.648, 2.76, 0.653, 2.75]}, {"time": 0.6667, "value": 2.59, "curve": [0.678, 2.39, 0.733, 2.53]}, {"time": 0.7333, "value": -9.43, "curve": [0.745, -2.48, 0.782, 3.12]}, {"time": 0.8, "value": 4.28, "curve": [0.832, 6.32, 0.895, 8.46]}, {"time": 0.9333, "value": 8.49, "curve": [0.986, 8.53, 1.051, 6.38]}, {"time": 1.0667, "value": 2.28, "curve": [1.078, 4.17, 1.103, 5.86]}, {"time": 1.1333, "value": 5.88, "curve": [1.191, 5.93, 1.209, 4.56]}, {"time": 1.2333, "value": 2.52}]}, "rear-thigh": {"rotate": [{"curve": [0.033, 0, 0.12, 50.26]}, {"time": 0.1333, "value": 57.3, "curve": [0.164, 73.34, 0.274, 147.18]}, {"time": 0.3333, "value": 147.1, "curve": [0.475, 146.45, 0.583, 95.72]}, {"time": 0.6, "value": 79.66, "curve": [0.62, 94.74, 0.732, 103.15]}, {"time": 0.7667, "value": 103.02, "curve": [0.812, 102.85, 0.897, 95.75]}, {"time": 0.9333, "value": 83.01}]}, "rear-shin": {"rotate": [{"curve": [0.021, -16.65, 0.091, -54.82]}, {"time": 0.1667, "value": -55.29, "curve": [0.187, -55.42, 0.213, -52.52]}, {"time": 0.2333, "value": -45.98, "curve": [0.242, -43.1, 0.311, -12.73]}, {"time": 0.3333, "value": -6.32, "curve": [0.356, 0.13, 0.467, 24.5]}, {"time": 0.5, "value": 24.5, "curve": [0.543, 24.5, 0.56, 3.78]}, {"time": 0.5667, "value": -3.53, "curve": [0.585, 3.86, 0.659, 16.63]}, {"time": 0.7, "value": 16.56, "curve": [0.782, 16.43, 0.896, 8.44]}, {"time": 0.9333, "value": 4.04, "curve": [0.956, 6.84, 1.008, 8.41]}, {"time": 1.0333, "value": 8.41, "curve": [1.067, 8.41, 1.122, 8.14]}, {"time": 1.1667, "value": 5.8}]}, "rear-foot": {"rotate": [{"value": -0.28, "curve": [0.033, -0.28, 0.256, -66.71]}, {"time": 0.3667, "value": -66.84, "curve": [0.418, -66.91, 0.499, -21.79]}, {"time": 0.6, "value": -21.52, "curve": [0.652, -21.38, 0.665, -53.96]}, {"time": 0.7, "value": -54.26, "curve": [0.757, -53.96, 0.843, -2.07]}, {"time": 0.9333, "value": -1.47, "curve": [0.968, -2.07, 0.975, -19.96]}, {"time": 1, "value": -19.96, "curve": [1.025, -19.96, 1.075, -12.42]}, {"time": 1.1, "value": -12.42, "curve": [1.133, -12.42, 1.2, -18.34]}, {"time": 1.2333, "value": -18.34}]}, "front-foot": {"rotate": [{"curve": [0.008, -11.33, 0.108, -57.71]}, {"time": 0.1333, "value": -57.71, "curve": [0.175, -57.71, 0.229, 19.73]}, {"time": 0.3, "value": 19.34, "curve": [0.354, 19.34, 0.4, -57.76]}, {"time": 0.4333, "value": -57.76, "curve": [0.458, -57.76, 0.511, -3.56]}, {"time": 0.5333, "value": 3.7, "curve": [0.563, 13.29, 0.633, 15.79]}, {"time": 0.6667, "value": 15.79, "curve": [0.7, 15.79, 0.767, -48.75]}, {"time": 0.8, "value": -48.75, "curve": [0.842, -48.75, 0.925, 4.7]}, {"time": 0.9667, "value": 4.7, "curve": [1, 4.7, 1.067, -22.9]}, {"time": 1.1, "value": -22.9, "curve": [1.142, -22.9, 1.225, -13.28]}, {"time": 1.2667, "value": -13.28}]}, "rear-foot-target": {"rotate": [{"value": -0.28}]}, "front-foot-tip": {"rotate": [{"value": -0.28, "curve": [0.008, -0.28, 0.003, -66.62]}, {"time": 0.0667, "value": -65.75, "curve": [0.166, -64.42, 0.234, 14.35]}, {"time": 0.2667, "value": 38.25, "curve": [0.294, 57.91, 0.392, 89.79]}, {"time": 0.4667, "value": 90.73, "curve": [0.483, 90.73, 0.55, 177.66]}, {"time": 0.5667, "value": 177.66, "curve": [0.733, 176.24, 0.75, 11.35]}, {"time": 0.8, "value": 11.35, "curve": [0.886, 12.29, 0.911, 47.88]}, {"time": 0.9333, "value": 56.77, "curve": [0.967, 70.59, 1.05, 86.46]}, {"time": 1.1, "value": 86.46, "curve": [1.187, 86.46, 1.214, 66.44]}, {"time": 1.3333, "value": 64.55}]}, "back-foot-tip": {"rotate": [{"value": -0.28, "curve": [0, -7.97, 0.027, -18.69]}, {"time": 0.0667, "value": -19, "curve": [0.166, -19.3, 0.208, 15.58]}, {"time": 0.2667, "value": 45.95, "curve": [0.306, 66.24, 0.378, 99.08]}, {"time": 0.4333, "value": 99.08, "curve": [0.497, 98.62, 0.488, -1.2]}, {"time": 0.5667, "value": -1.32, "curve": [0.637, -0.84, 0.687, 94.41]}, {"time": 0.7333, "value": 94.33, "curve": [0.832, 94.16, 0.895, 29.6]}, {"time": 0.9667, "value": 28.67, "curve": [1.026, 28.67, 1.045, 53.14]}, {"time": 1.1, "value": 53.38}]}, "hair4": {"rotate": [{"curve": [0.011, 4.5, 0.05, 11.42]}, {"time": 0.0667, "value": 11.42, "curve": [0.1, 11.42, 0.136, -5.92]}, {"time": 0.1667, "value": -10.54, "curve": [0.206, -16.51, 0.327, -22]}, {"time": 0.3667, "value": -24.47, "curve": [0.413, -27.37, 0.467, -43.99]}, {"time": 0.5, "value": -43.99, "curve": [0.533, -43.99, 0.552, 12.12]}, {"time": 0.6333, "value": 11.85, "curve": [0.714, 11.59, 0.758, -34.13]}, {"time": 0.8, "value": -34.13, "curve": [0.858, -34.13, 1.015, -12.47]}, {"time": 1.0667, "value": -8.85, "curve": [1.121, -5.07, 1.219, -0.02]}, {"time": 1.3333, "value": 1.29, "curve": [1.509, 3.3, 1.763, 2.75]}, {"time": 1.8667, "value": 2.78, "curve": [1.974, 2.81, 2.108, 2.81]}, {"time": 2.2, "value": 2.78, "curve": [2.315, 2.74, 2.374, 1.22]}, {"time": 2.4667, "value": 1.18, "curve": [2.525, 1.18, 2.608, 10.79]}, {"time": 2.6667, "value": 10.79, "curve": [2.725, 10.79, 2.893, 4.72]}, {"time": 3.0333, "value": 4.72, "curve": [3.117, 4.72, 3.283, 7.93]}, {"time": 3.3667, "value": 7.93, "curve": [3.492, 7.93, 3.775, 6.93]}, {"time": 3.9, "value": 6.93, "curve": [3.981, 6.93, 4.094, 6.9]}, {"time": 4.2, "value": 8.44, "curve": [4.267, 9.42, 4.401, 16.61]}, {"time": 4.5, "value": 16.33, "curve": [4.582, 16.12, 4.709, 9.94]}, {"time": 4.7333, "value": 6.51, "curve": [4.747, 4.57, 4.779, -1.76]}, {"time": 4.8, "value": -1.75, "curve": [4.823, -1.73, 4.82, 4.47]}, {"time": 4.8667, "value": 6.04, "curve": [4.899, 7.14, 4.913, 6.93]}, {"time": 4.9333, "value": 6.93}]}, "hair2": {"rotate": [{"value": 10.61, "curve": [0.075, 10.61, 0.05, 12.67]}, {"time": 0.0667, "value": 12.67, "curve": [0.123, 12.67, 0.194, -16.51]}, {"time": 0.2, "value": -19.87, "curve": [0.207, -23.48, 0.236, -31.68]}, {"time": 0.3, "value": -31.8, "curve": [0.356, -31.9, 0.437, -25.61]}, {"time": 0.4667, "value": -19.29, "curve": [0.485, -15.33, 0.529, 6.48]}, {"time": 0.5667, "value": 6.67, "curve": [0.628, 6.97, 0.65, -46.39]}, {"time": 0.7333, "value": -46.3, "curve": [0.843, -46.17, 0.941, -33.37]}, {"time": 0.9667, "value": -23.17, "curve": [0.972, -20.98, 1.047, 15.21]}, {"time": 1.1, "value": 15.21, "curve": [1.142, 15.21, 1.183, 10.73]}, {"time": 1.2667, "value": 10.61, "curve": [1.45, 10.34, 1.817, 10.61]}, {"time": 2, "value": 10.61, "curve": [2.075, 10.61, 2.225, 16.9]}, {"time": 2.3, "value": 16.9, "curve": [2.327, 16.9, 2.347, 6.81]}, {"time": 2.4, "value": 6.83, "curve": [2.492, 6.87, 2.602, 17.39]}, {"time": 2.6667, "value": 17.39, "curve": [2.742, 17.39, 2.892, 10.67]}, {"time": 2.9667, "value": 10.64, "curve": [3.187, 10.57, 3.344, 10.73]}, {"time": 3.6, "value": 11.4, "curve": [3.766, 11.83, 3.874, 14.87]}, {"time": 3.9333, "value": 14.83, "curve": [4.022, 14.76, 4.208, 9.49]}, {"time": 4.3, "value": 9.54, "curve": [4.391, 9.58, 4.441, 14.82]}, {"time": 4.5333, "value": 14.84, "curve": [4.642, 14.88, 4.692, 1.17]}, {"time": 4.7667, "value": 1.24, "curve": [4.823, 1.3, 4.818, 18.35]}, {"time": 4.8667, "value": 18.38, "curve": [4.905, 18.41, 4.901, 10.61]}, {"time": 4.9333, "value": 10.61}]}, "torso2": {"rotate": [{"curve": [0.048, 0, 0.129, -12.73]}, {"time": 0.1667, "value": -15.95, "curve": [0.221, -20.66, 0.254, -21.62]}, {"time": 0.3, "value": -21.59, "curve": [0.458, -21.46, 0.46, -1.67]}, {"time": 0.6333, "value": -1.71, "curve": [0.71, -1.73, 0.715, -4]}, {"time": 0.7667, "value": -3.97, "curve": [0.866, -3.92, 0.84, 0.02]}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": [2.275, 0, 2.867, -5.8]}, {"time": 3.1, "value": -6.44, "curve": [3.327, -7.06, 3.71, -6.23]}, {"time": 3.9333, "value": -5.41, "curve": [4.168, -4.53, 4.488, -2.83]}, {"time": 4.8}]}, "torso3": {"rotate": [{"curve": [0.025, 0, 0.09, -3.66]}, {"time": 0.1, "value": -4.55, "curve": [0.143, -8.4, 0.223, -17.07]}, {"time": 0.2333, "value": -18.31, "curve": [0.282, -24.44, 0.35, -29]}, {"time": 0.3667, "value": -30.07, "curve": [0.405, -32.58, 0.442, -33.03]}, {"time": 0.4667, "value": -32.99, "curve": [0.491, -33.04, 0.505, -23.56]}, {"time": 0.5333, "value": -23.55, "curve": [0.571, -23.67, 0.599, -27.21]}, {"time": 0.6333, "value": -27.21, "curve": [0.669, -27.2, 0.742, -10.43]}, {"time": 0.7667, "value": -7.79, "curve": [0.788, -5.53, 0.796, -4.42]}, {"time": 0.8333, "value": -2.9, "curve": [0.875, -1.21, 0.933, 0]}, {"time": 0.9667, "curve": "stepped"}, {"time": 2.4333, "curve": [2.517, 0, 2.683, 4.63]}, {"time": 2.7667, "value": 4.66, "curve": [3.084, 4.76, 3.248, 4.37]}, {"time": 3.4, "value": 3.74, "curve": [3.596, 2.92, 3.755, 2.18]}, {"time": 3.8667, "value": 1.72, "curve": [4.136, 0.59, 4.471, 0]}, {"time": 4.8}]}, "hair3": {"rotate": [{"curve": [0, 0, 0.041, 10.74]}, {"time": 0.0667, "value": 14.16, "curve": [0.075, 15.22, 0.148, 18.04]}, {"time": 0.2, "value": 18.13, "curve": [0.251, 18.23, 0.307, -4.75]}, {"time": 0.3667, "value": -5.06, "curve": [0.412, -5.3, 0.47, -0.96]}, {"time": 0.5, "value": 2.21, "curve": [0.512, 3.48, 0.595, 20.31]}, {"time": 0.6333, "value": 24.87, "curve": [0.647, 26.53, 0.719, 29.33]}, {"time": 0.8, "value": 29.22, "curve": [0.859, 29.14, 0.9, 28.48]}, {"time": 0.9333, "value": 26.11, "curve": [0.981, 22.72, 0.998, 2.06]}, {"time": 1.1, "value": 2.21}]}, "hair1": {"rotate": [{"curve": [0.047, -0.21, 0.048, 7.86]}, {"time": 0.0667, "value": 13.27, "curve": [0.083, 18.05, 0.135, 24.44]}, {"time": 0.2, "value": 24.02, "curve": [0.225, 24.02, 0.28, 6.32]}, {"time": 0.3, "value": 3.1, "curve": [0.323, -0.58, 0.382, -7.12]}, {"time": 0.4667, "value": -7.45, "curve": [0.512, -7.66, 0.538, 12.13]}, {"time": 0.5667, "value": 16.46, "curve": [0.609, 22.72, 0.672, 27.4]}, {"time": 0.7333, "value": 27.55, "curve": [0.827, 27.4, 0.933, 23.23]}, {"time": 0.9667, "value": 19.11, "curve": [0.998, 15.27, 1.092, -2.53]}, {"time": 1.1333, "value": -2.53, "curve": [1.158, -2.53, 1.208, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 2, "curve": [2.075, 0, 2.248, 0.35]}, {"time": 2.3333, "value": 0.78, "curve": [2.585, 2.06, 2.805, 3.46]}, {"time": 3.2, "value": 3.5, "curve": [3.593, 3.54, 3.979, 2.36]}, {"time": 4.1667, "value": 1.55, "curve": [4.391, 0.59, 4.447, 0.04]}, {"time": 4.6, "value": 0.04, "curve": [4.642, 0.04, 4.742, 0]}, {"time": 4.9333}]}, "head-control": {"translate": [{"curve": [0.025, 0, 0.09, 1.43, 0.025, 0, 0.075, -34.76]}, {"time": 0.1, "x": 1.59, "y": -34.76, "curve": [0.214, 3.33, 0.375, 5.34, 0.192, -34.76, 0.441, -21.17]}, {"time": 0.4667, "x": 5.34, "y": -12.57, "curve": [0.492, 5.34, 0.55, 5.24, 0.482, -7.36, 0.504, 4.03]}, {"time": 0.5667, "x": 5.11, "y": 4.01, "curve": [0.658, 4.45, 0.679, 3.19, 0.649, 3.98, 0.642, -16.84]}, {"time": 0.7, "x": 2.8, "y": -16.74, "curve": [0.787, 1.15, 0.881, -1.29, 0.772, -16.62, 0.82, 8.95]}, {"time": 0.9, "x": -1.72, "y": 8.91, "curve": [0.961, -3.06, 1.025, -3.58, 0.975, 8.87, 0.951, -1.37]}, {"time": 1.1, "x": -3.58, "y": -1.45, "curve": [1.292, -3.58, 2.002, -2.4, 1.292, -1.56, 1.975, -1.45]}, {"time": 2.1667, "x": -1.39, "y": -1.45, "curve": [2.25, -0.88, 2.503, 1.38, 2.283, -1.45, 2.603, -12.44]}, {"time": 2.6667, "x": 2.13, "y": -14.45, "curve": [2.766, 2.59, 2.999, 2.81, 2.835, -19.73, 3.003, -25.2]}, {"time": 3.1333, "x": 2.91, "y": -26.08, "curve": [3.392, 3.1, 4.199, 4.05, 3.483, -28.44, 4.129, -27.23]}, {"time": 4.3667, "x": 4.81, "y": -19.59, "curve": [4.429, 5.1, 4.594, 8.54, 4.538, -14.08, 4.583, -7.88]}, {"time": 4.6667, "x": 8.65, "y": -4.56, "curve": [4.794, 8.86, 4.806, 5.93, 4.691, -3.59, 4.8, -1.61]}, {"time": 4.9333, "x": 5.8, "y": -1.99}]}}, "ik": {"front-foot-ik": [{"mix": 0}], "front-leg-ik": [{"mix": 0, "bendPositive": false}], "rear-foot-ik": [{"mix": 0.005}], "rear-leg-ik": [{"mix": 0.005, "bendPositive": false}]}}, "hoverboard": {"slots": {"exhaust1": {"attachment": [{"name": "hoverglow-small"}]}, "exhaust2": {"attachment": [{"name": "hoverglow-small"}]}, "exhaust3": {"attachment": [{"name": "hoverglow-small"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "hoverboard-board": {"attachment": [{"name": "hoverboard-board"}]}, "hoverboard-thruster-front": {"attachment": [{"name": "hoverboard-thruster"}]}, "hoverboard-thruster-rear": {"attachment": [{"name": "hoverboard-thruster"}]}, "hoverglow-front": {"attachment": [{"name": "hoverglow-small"}]}, "hoverglow-rear": {"attachment": [{"name": "hoverglow-small"}]}, "side-glow1": {"attachment": [{"name": "hoverglow-small"}, {"time": 0.9667}]}, "side-glow2": {"attachment": [{"time": 0.0667, "name": "hoverglow-small"}, {"time": 1}]}, "side-glow3": {"attachment": [{"name": "hoverglow-small"}, {"time": 0.9667}]}}, "bones": {"hoverboard-controller": {"translate": [{"x": 319.55, "y": -1.59, "curve": [0.064, 319.55, 0.2, 347.85, 0.058, -1.2, 0.2, 23.11]}, {"time": 0.2667, "x": 347.66, "y": 39.62, "curve": [0.35, 347.41, 0.476, 341.47, 0.323, 53.58, 0.44, 85.82]}, {"time": 0.5333, "x": 338.47, "y": 85.72, "curve": [0.603, 334.83, 0.913, 319.65, 0.621, 85.62, 0.88, -1.53]}, {"time": 1, "x": 319.55, "y": -1.59}]}, "hip": {"translate": [{"x": -53.49, "y": 32.14, "curve": [0.061, -53.77, 0.093, -51.81, 0.044, 16.34, 0.063, 9.67]}, {"time": 0.1333, "x": -49.31, "y": 7.01, "curve": [0.3, -35.27, 0.461, -20.06, 0.314, 9.52, 0.408, 121.09]}, {"time": 0.5667, "x": -20.06, "y": 122.72, "curve": [0.716, -20.09, 0.912, -53.29, 0.753, 121.8, 0.946, 51.85]}, {"time": 1, "x": -53.49, "y": 32.14}]}, "exhaust1": {"scale": [{"x": 1.593, "y": 0.964, "curve": [0.033, 1.593, 0.1, 1, 0.033, 0.964, 0.1, 0.713]}, {"time": 0.1333, "y": 0.713, "curve": [0.15, 1, 0.183, 1.774, 0.15, 0.713, 0.183, 0.883]}, {"time": 0.2, "x": 1.774, "y": 0.883, "curve": [0.242, 1.774, 0.325, 1.181, 0.242, 0.883, 0.325, 0.649]}, {"time": 0.3667, "x": 1.181, "y": 0.649, "curve": [0.408, 1.181, 0.492, 1.893, 0.408, 0.649, 0.492, 0.819]}, {"time": 0.5333, "x": 1.893, "y": 0.819, "curve": [0.558, 1.893, 0.608, 1.18, 0.558, 0.819, 0.608, 0.686]}, {"time": 0.6333, "x": 1.18, "y": 0.686, "curve": [0.658, 1.18, 0.708, 1.903, 0.658, 0.686, 0.708, 0.855]}, {"time": 0.7333, "x": 1.903, "y": 0.855, "curve": [0.767, 1.903, 0.833, 1.311, 0.767, 0.855, 0.833, 0.622]}, {"time": 0.8667, "x": 1.311, "y": 0.622, "curve": [0.9, 1.311, 0.967, 1.593, 0.9, 0.622, 0.967, 0.964]}, {"time": 1, "x": 1.593, "y": 0.964}]}, "exhaust2": {"scale": [{"x": 1.88, "y": 0.832, "curve": [0.025, 1.88, 0.075, 1.311, 0.025, 0.832, 0.075, 0.686]}, {"time": 0.1, "x": 1.311, "y": 0.686, "curve": [0.133, 1.311, 0.2, 2.01, 0.133, 0.686, 0.208, 0.736]}, {"time": 0.2333, "x": 2.01, "y": 0.769, "curve": [0.267, 2.01, 0.333, 1, 0.282, 0.831, 0.333, 0.91]}, {"time": 0.3667, "y": 0.91, "curve": [0.4, 1, 0.467, 1.699, 0.4, 0.91, 0.474, 0.891]}, {"time": 0.5, "x": 1.699, "y": 0.86, "curve": [0.517, 1.699, 0.55, 1.181, 0.54, 0.813, 0.55, 0.713]}, {"time": 0.5667, "x": 1.181, "y": 0.713, "curve": [0.617, 1.181, 0.717, 1.881, 0.617, 0.713, 0.717, 0.796]}, {"time": 0.7667, "x": 1.881, "y": 0.796, "curve": [0.8, 1.881, 0.867, 1.3, 0.8, 0.796, 0.867, 0.649]}, {"time": 0.9, "x": 1.3, "y": 0.649, "curve": [0.925, 1.3, 0.975, 1.88, 0.925, 0.649, 0.975, 0.832]}, {"time": 1, "x": 1.88, "y": 0.832}]}, "hoverboard-thruster-front": {"rotate": [{"curve": [0.125, 0, 0.375, 24.06]}, {"time": 0.5, "value": 24.06, "curve": [0.625, 24.06, 0.875, 0]}, {"time": 1}]}, "hoverglow-front": {"scale": [{"x": 0.849, "y": 1.764, "curve": [0.017, 0.849, 0.05, 0.835, 0.017, 1.764, 0.05, 2.033]}, {"time": 0.0667, "x": 0.835, "y": 2.033, "curve": [0.092, 0.835, 0.142, 0.752, 0.092, 2.033, 0.142, 1.584]}, {"time": 0.1667, "x": 0.752, "y": 1.584, "curve": [0.183, 0.752, 0.217, 0.809, 0.183, 1.584, 0.217, 1.71]}, {"time": 0.2333, "x": 0.809, "y": 1.71, "curve": [0.25, 0.809, 0.283, 0.717, 0.25, 1.71, 0.283, 1.45]}, {"time": 0.3, "x": 0.717, "y": 1.45, "curve": [0.317, 0.717, 0.35, 0.777, 0.317, 1.45, 0.35, 1.698]}, {"time": 0.3667, "x": 0.777, "y": 1.698, "curve": [0.4, 0.781, 0.45, 0.685, 0.375, 1.698, 0.45, 1.173]}, {"time": 0.4667, "x": 0.685, "y": 1.173, "curve": [0.492, 0.685, 0.542, 0.825, 0.492, 1.173, 0.542, 1.572]}, {"time": 0.5667, "x": 0.825, "y": 1.572, "curve": [0.611, 0.816, 0.63, 0.727, 0.611, 1.577, 0.606, 1.255]}, {"time": 0.6667, "x": 0.725, "y": 1.241, "curve": [0.692, 0.725, 0.742, 0.895, 0.692, 1.241, 0.749, 1.799]}, {"time": 0.7667, "x": 0.895, "y": 1.857, "curve": [0.783, 0.895, 0.796, 0.892, 0.796, 1.955, 0.817, 1.962]}, {"time": 0.8333, "x": 0.845, "y": 1.962, "curve": [0.845, 0.831, 0.883, 0.802, 0.85, 1.962, 0.872, 1.704]}, {"time": 0.9, "x": 0.802, "y": 1.491, "curve": [0.917, 0.802, 0.95, 0.845, 0.907, 1.441, 0.936, 1.508]}, {"time": 0.9667, "x": 0.845, "y": 1.627, "curve": [0.975, 0.845, 0.992, 0.849, 0.973, 1.652, 0.992, 1.764]}, {"time": 1, "x": 0.849, "y": 1.764}]}, "hoverboard-thruster-rear": {"rotate": [{"curve": [0.125, 0, 0.375, 24.06]}, {"time": 0.5, "value": 24.06, "curve": [0.625, 24.06, 0.875, 0]}, {"time": 1}]}, "hoverglow-rear": {"scale": [{"x": 0.845, "y": 1.31, "curve": [0.017, 0.845, 0.117, 0.899, 0.017, 1.31, 0.117, 2.033]}, {"time": 0.1333, "x": 0.899, "y": 2.033, "curve": [0.15, 0.899, 0.183, 0.752, 0.15, 2.033, 0.183, 1.574]}, {"time": 0.2, "x": 0.752, "y": 1.574, "curve": [0.225, 0.752, 0.275, 0.809, 0.225, 1.574, 0.275, 1.71]}, {"time": 0.3, "x": 0.809, "y": 1.71, "curve": [0.317, 0.809, 0.35, 0.717, 0.317, 1.71, 0.35, 1.397]}, {"time": 0.3667, "x": 0.717, "y": 1.397, "curve": [0.383, 0.717, 0.417, 0.777, 0.383, 1.397, 0.417, 1.45]}, {"time": 0.4333, "x": 0.777, "y": 1.45, "curve": [0.45, 0.777, 0.496, 0.689, 0.45, 1.45, 0.481, 1.168]}, {"time": 0.5333, "x": 0.685, "y": 1.173, "curve": [0.565, 0.682, 0.617, 0.758, 0.575, 1.177, 0.617, 1.297]}, {"time": 0.6333, "x": 0.758, "y": 1.297, "curve": [0.658, 0.758, 0.708, 0.725, 0.658, 1.297, 0.708, 1.241]}, {"time": 0.7333, "x": 0.725, "y": 1.241, "curve": [0.772, 0.732, 0.796, 0.893, 0.782, 1.238, 0.778, 1.854]}, {"time": 0.8333, "x": 0.895, "y": 1.857, "curve": [0.878, 0.9, 0.992, 0.845, 0.88, 1.86, 0.992, 1.31]}, {"time": 1, "x": 0.845, "y": 1.31}]}, "front-upper-arm": {"rotate": [{"value": -85.92, "curve": [0.08, -85.59, 0.284, -62.7]}, {"time": 0.3667, "value": -55.14, "curve": [0.438, -48.65, 0.551, -43.21]}, {"time": 0.6333, "value": -43.21, "curve": [0.716, -43.22, 0.908, -85.92]}, {"time": 1, "value": -85.92}], "translate": [{"x": -0.59, "y": -2.94, "curve": [0.1, -1.21, 0.275, -1.74, 0.092, -2.94, 0.275, -6.39]}, {"time": 0.3667, "x": -1.74, "y": -6.39, "curve": [0.433, -1.74, 0.567, 0.72, 0.433, -6.39, 0.587, -4.48]}, {"time": 0.6333, "x": 0.72, "y": -4.21, "curve": [0.725, 0.72, 0.908, -0.08, 0.743, -3.57, 0.908, -2.94]}, {"time": 1, "x": -0.59, "y": -2.94}]}, "front-fist": {"rotate": [{"value": 7.61, "curve": [0.143, 7.62, 0.247, -23.17]}, {"time": 0.2667, "value": -26.56, "curve": [0.281, -29.08, 0.351, -37.36]}, {"time": 0.4333, "value": -37.2, "curve": [0.513, -37.05, 0.562, -29.88]}, {"time": 0.6, "value": -25.18, "curve": [0.621, -22.58, 0.694, -3.98]}, {"time": 0.8, "value": 3.63, "curve": [0.861, 8.03, 0.946, 7.57]}, {"time": 1, "value": 7.61}], "translate": [{"curve": [0.117, 0, 0.35, 0.52, 0.117, 0, 0.35, -3.27]}, {"time": 0.4667, "x": 0.52, "y": -3.27, "curve": [0.6, 0.52, 0.867, 0, 0.6, -3.27, 0.867, 0]}, {"time": 1}], "shear": [{"y": 19.83, "curve": [0.117, 0, 0.35, 15.28, 0.117, 19.83, 0.35, 28.31]}, {"time": 0.4667, "x": 15.28, "y": 28.31, "curve": [0.6, 15.28, 0.867, 0, 0.6, 28.31, 0.867, 19.83]}, {"time": 1, "y": 19.83}]}, "board-ik": {"translate": [{"x": 393.62, "curve": [0.083, 393.62, 0.25, 393.48, 0.083, 0, 0.25, 117.69]}, {"time": 0.3333, "x": 393.48, "y": 117.69, "curve": [0.375, 393.48, 0.458, 393.62, 0.375, 117.69, 0.458, 83.82]}, {"time": 0.5, "x": 393.62, "y": 83.82}, {"time": 0.6667, "x": 393.62, "y": 30.15}, {"time": 1, "x": 393.62}]}, "front-thigh": {"translate": [{"x": -7.49, "y": 8.51}]}, "front-leg-target": {"translate": [{"time": 0.3667, "curve": [0.428, 10.83, 0.567, 12.78, 0.414, 7.29, 0.567, 8.79]}, {"time": 0.6, "x": 12.78, "y": 8.79, "curve": [0.692, 12.78, 0.772, 11.27, 0.692, 8.79, 0.766, 8.62]}, {"time": 0.8667}]}, "rear-leg-target": {"translate": [{"time": 0.4667, "curve": [0.492, 0, 0.534, 4.47, 0.492, 0, 0.542, 1.63]}, {"time": 0.5667, "x": 4.53, "y": 1.77, "curve": [0.622, 4.64, 0.717, 3.31, 0.615, 2.06, 0.71, 2.1]}, {"time": 0.8}]}, "exhaust3": {"scale": [{"x": 1.882, "y": 0.81, "curve": [0.017, 1.882, 0.167, 1.3, 0.017, 0.81, 0.167, 0.649]}, {"time": 0.2, "x": 1.3, "y": 0.649, "curve": [0.225, 1.3, 0.275, 2.051, 0.225, 0.649, 0.275, 0.984]}, {"time": 0.3, "x": 2.051, "y": 0.984, "curve": [0.325, 2.051, 0.375, 1.311, 0.325, 0.984, 0.384, 0.715]}, {"time": 0.4, "x": 1.311, "y": 0.686, "curve": [0.433, 1.311, 0.5, 1.86, 0.426, 0.638, 0.5, 0.537]}, {"time": 0.5333, "x": 1.86, "y": 0.537, "curve": [0.567, 1.86, 0.633, 1.187, 0.567, 0.537, 0.604, 0.854]}, {"time": 0.6667, "x": 1.187, "y": 0.854, "curve": [0.7, 1.187, 0.767, 1.549, 0.707, 0.854, 0.774, 0.775]}, {"time": 0.8, "x": 1.549, "y": 0.746, "curve": [0.817, 1.549, 0.85, 1.181, 0.815, 0.729, 0.85, 0.713]}, {"time": 0.8667, "x": 1.181, "y": 0.713, "curve": [0.9, 1.181, 0.967, 1.882, 0.9, 0.713, 0.967, 0.81]}, {"time": 1, "x": 1.882, "y": 0.81}]}, "side-glow1": {"rotate": [{"value": 51.12, "curve": "stepped"}, {"time": 0.0667, "value": 43.82, "curve": "stepped"}, {"time": 0.1, "value": 40.95, "curve": "stepped"}, {"time": 0.1667, "value": 27.78, "curve": "stepped"}, {"time": 0.2, "value": 10.24, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.8, "value": -25.81}], "translate": [{"x": 338.28, "y": 40.22, "curve": "stepped"}, {"time": 0.0667, "x": 331.2, "y": 30.39, "curve": "stepped"}, {"time": 0.1, "x": 318.63, "y": 20.59, "curve": "stepped"}, {"time": 0.1667, "x": 302.45, "y": 9.64, "curve": "stepped"}, {"time": 0.2, "x": 276.87, "y": 1.13, "curve": "stepped"}, {"time": 0.2667, "x": 248.16, "curve": "stepped"}, {"time": 0.3, "x": 221.36, "curve": "stepped"}, {"time": 0.3667, "x": 195.69, "curve": "stepped"}, {"time": 0.4, "x": 171.08, "curve": "stepped"}, {"time": 0.4667, "x": 144.84, "curve": "stepped"}, {"time": 0.5, "x": 121.22, "curve": "stepped"}, {"time": 0.5667, "x": 91.98, "curve": "stepped"}, {"time": 0.6, "x": 62.63, "curve": "stepped"}, {"time": 0.6667, "x": 30.78, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7667, "x": -28.45, "curve": "stepped"}, {"time": 0.8, "x": -67.49, "y": 16.82, "curve": "stepped"}, {"time": 0.8667, "x": -83.07, "y": 24.36, "curve": "stepped"}, {"time": 0.9, "x": -93.81, "y": 29.55}], "scale": [{"x": 0.535, "curve": "stepped"}, {"time": 0.0667, "x": 0.594, "curve": "stepped"}, {"time": 0.1, "x": 0.844, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.8, "x": 0.534, "curve": "stepped"}, {"time": 0.8667, "x": 0.428, "y": 0.801, "curve": "stepped"}, {"time": 0.9, "x": 0.349, "y": 0.654}]}, "side-glow2": {"rotate": [{"time": 0.0667, "value": 51.12, "curve": "stepped"}, {"time": 0.1, "value": 43.82, "curve": "stepped"}, {"time": 0.1667, "value": 40.95, "curve": "stepped"}, {"time": 0.2, "value": 27.78, "curve": "stepped"}, {"time": 0.2667, "value": 10.24, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8667, "value": -25.81}], "translate": [{"time": 0.0667, "x": 338.28, "y": 40.22, "curve": "stepped"}, {"time": 0.1, "x": 331.2, "y": 30.39, "curve": "stepped"}, {"time": 0.1667, "x": 318.63, "y": 20.59, "curve": "stepped"}, {"time": 0.2, "x": 302.45, "y": 9.64, "curve": "stepped"}, {"time": 0.2667, "x": 276.87, "y": 1.13, "curve": "stepped"}, {"time": 0.3, "x": 248.16, "curve": "stepped"}, {"time": 0.3667, "x": 221.36, "curve": "stepped"}, {"time": 0.4, "x": 195.69, "curve": "stepped"}, {"time": 0.4667, "x": 171.08, "curve": "stepped"}, {"time": 0.5, "x": 144.84, "curve": "stepped"}, {"time": 0.5667, "x": 121.22, "curve": "stepped"}, {"time": 0.6, "x": 91.98, "curve": "stepped"}, {"time": 0.6667, "x": 62.63, "curve": "stepped"}, {"time": 0.7, "x": 30.78, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8, "x": -28.45, "curve": "stepped"}, {"time": 0.8667, "x": -67.49, "y": 16.82, "curve": "stepped"}, {"time": 0.9, "x": -83.07, "y": 24.36, "curve": "stepped"}, {"time": 0.9667, "x": -93.81, "y": 29.55}], "scale": [{"time": 0.0667, "x": 0.535, "curve": "stepped"}, {"time": 0.1, "x": 0.594, "curve": "stepped"}, {"time": 0.1667, "x": 0.844, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.8667, "x": 0.534, "curve": "stepped"}, {"time": 0.9, "x": 0.428, "y": 0.801, "curve": "stepped"}, {"time": 0.9667, "x": 0.349, "y": 0.654}]}, "torso": {"rotate": [{"value": -34.73, "curve": [0.034, -36.31, 0.162, -39.33]}, {"time": 0.2667, "value": -39.37, "curve": [0.384, -39.37, 0.491, -29.52]}, {"time": 0.5, "value": -28.86, "curve": [0.525, -26.95, 0.571, -21.01]}, {"time": 0.6333, "value": -21.01, "curve": [0.725, -21.01, 0.969, -33.35]}, {"time": 1, "value": -34.73}]}, "neck": {"rotate": [{"value": 10.2, "curve": [0.07, 12.09, 0.189, 16.03]}, {"time": 0.2667, "value": 16.14, "curve": [0.333, 16.14, 0.449, 8.03]}, {"time": 0.5, "value": 5.83, "curve": [0.542, 4.02, 0.6, 2.68]}, {"time": 0.6333, "value": 2.68, "curve": [0.725, 2.68, 0.943, 8.57]}, {"time": 1, "value": 10.2}]}, "head": {"rotate": [{"value": 10.2, "curve": [0.044, 11.52, 0.2, 16.12]}, {"time": 0.2667, "value": 16.14, "curve": [0.375, 16.17, 0.492, 2.65]}, {"time": 0.6333, "value": 2.68, "curve": [0.725, 2.7, 0.963, 9.26]}, {"time": 1, "value": 10.2}], "translate": [{"curve": [0.03, -0.24, 0.2, -4.22, 0.051, -1.06, 0.2, -3.62]}, {"time": 0.2667, "x": -4.22, "y": -3.62, "curve": [0.358, -4.22, 0.542, 0.84, 0.358, -3.62, 0.542, 6.01]}, {"time": 0.6333, "x": 0.84, "y": 6.01, "curve": [0.725, 0.84, 0.939, 0.32, 0.725, 6.01, 0.945, 1.14]}, {"time": 1}]}, "front-bracer": {"rotate": [{"value": -11.18, "curve": [0.064, -14.82, 0.25, -20.01]}, {"time": 0.3333, "value": -20.01, "curve": [0.429, -20.12, 0.58, 5.12]}, {"time": 0.6, "value": 8.67, "curve": [0.617, 11.72, 0.687, 20.52]}, {"time": 0.7667, "value": 20.55, "curve": [0.848, 20.7, 0.963, -9.43]}, {"time": 1, "value": -11.18}]}, "hair3": {"rotate": [{"value": 9.61, "curve": [0.014, 8.51, 0.075, 2.63]}, {"time": 0.1, "value": 2.63, "curve": [0.15, 2.63, 0.25, 13.52]}, {"time": 0.3, "value": 13.52, "curve": [0.35, 13.52, 0.45, 11.28]}, {"time": 0.5, "value": 11.28, "curve": [0.575, 11.28, 0.725, 18.13]}, {"time": 0.8, "value": 18.13, "curve": [0.85, 18.13, 0.978, 11.07]}, {"time": 1, "value": 9.61}]}, "hair4": {"rotate": [{"value": -17.7, "curve": [0.008, -17.7, 0.025, -23.73]}, {"time": 0.0333, "value": -23.73, "curve": [0.067, -23.73, 0.154, -4.4]}, {"time": 0.1667, "value": -1.92, "curve": [0.197, 4.09, 0.236, 12.91]}, {"time": 0.2667, "value": 17.56, "curve": [0.301, 22.68, 0.342, 27.97]}, {"time": 0.3667, "value": 27.97, "curve": [0.4, 27.97, 0.467, -1.45]}, {"time": 0.5, "value": -1.45, "curve": [0.517, -1.45, 0.55, 3.16]}, {"time": 0.5667, "value": 3.16, "curve": [0.583, 3.16, 0.617, -8.9]}, {"time": 0.6333, "value": -8.9, "curve": [0.642, -8.9, 0.658, -5.4]}, {"time": 0.6667, "value": -5.4, "curve": [0.683, -5.4, 0.717, -15.32]}, {"time": 0.7333, "value": -15.32, "curve": [0.75, -15.32, 0.783, -9.19]}, {"time": 0.8, "value": -9.19, "curve": [0.817, -9.19, 0.85, -23.6]}, {"time": 0.8667, "value": -23.6, "curve": [0.883, -23.6, 0.917, -17.38]}, {"time": 0.9333, "value": -17.38, "curve": [0.942, -17.38, 0.958, -20.46]}, {"time": 0.9667, "value": -20.46, "curve": [0.975, -20.46, 0.992, -17.7]}, {"time": 1, "value": -17.7}]}, "hair1": {"rotate": [{"value": 9.61, "curve": [0.06, 9.04, 0.25, 8.9]}, {"time": 0.3333, "value": 8.9, "curve": [0.392, 8.9, 0.508, 14.58]}, {"time": 0.5667, "value": 14.58, "curve": [0.675, 14.58, 0.956, 10.28]}, {"time": 1, "value": 9.61}]}, "hair2": {"rotate": [{"value": -3.82, "curve": [0.017, -3.82, 0.064, -9.16]}, {"time": 0.1333, "value": -9.09, "curve": [0.178, -9.04, 0.234, 1.29]}, {"time": 0.2667, "value": 5.98, "curve": [0.276, 7.27, 0.336, 17.1]}, {"time": 0.3667, "value": 17.1, "curve": [0.413, 17.1, 0.467, 1.59]}, {"time": 0.5, "value": 1.59, "curve": [0.533, 1.59, 0.567, 13.63]}, {"time": 0.6, "value": 13.63, "curve": [0.617, 13.63, 0.683, 0.78]}, {"time": 0.7, "value": 0.78, "curve": [0.717, 0.78, 0.75, 12.01]}, {"time": 0.7667, "value": 11.9, "curve": [0.792, 11.73, 0.817, -0.85]}, {"time": 0.8333, "value": -0.85, "curve": [0.85, -0.85, 0.88, 1.99]}, {"time": 0.9, "value": 1.82, "curve": [0.916, 1.68, 0.95, -6.9]}, {"time": 0.9667, "value": -6.9, "curve": [0.975, -6.9, 0.992, -3.82]}, {"time": 1, "value": -3.82}]}, "rear-upper-arm": {"rotate": [{"value": 31.65, "curve": [0.108, 31.65, 0.325, 13.01]}, {"time": 0.4333, "value": 13.01, "curve": [0.71, 13.01, 0.917, 31.65]}, {"time": 1, "value": 31.65}]}, "rear-bracer": {"rotate": [{"value": 31, "curve": [0.108, 31, 0.325, 12.76]}, {"time": 0.4333, "value": 12.79, "curve": [0.587, 12.82, 0.917, 31]}, {"time": 1, "value": 31}]}, "gun": {"rotate": [{"value": 1.95, "curve": [0.083, 1.95, 0.245, 36.73]}, {"time": 0.3333, "value": 36.71, "curve": [0.439, 36.69, 0.589, 10.68]}, {"time": 0.6333, "value": 8.75, "curve": [0.701, 5.81, 0.917, 1.95]}, {"time": 1, "value": 1.95}]}, "torso2": {"rotate": [{"curve": [0.033, 0, 0.1, 2.35]}, {"time": 0.1333, "value": 2.35, "curve": [0.225, 2.35, 0.408, -2.4]}, {"time": 0.5, "value": -2.4, "curve": [0.567, -2.4, 0.7, 1.44]}, {"time": 0.7667, "value": 1.44, "curve": [0.825, 1.44, 0.942, 0]}, {"time": 1}]}, "torso3": {"rotate": [{"curve": [0.063, 0.77, 0.106, 1.42]}, {"time": 0.1667, "value": 1.42, "curve": [0.259, 1.42, 0.344, -1.25]}, {"time": 0.4667, "value": -1.26, "curve": [0.656, -1.26, 0.917, -0.78]}, {"time": 1}]}, "head-control": {"translate": [{"x": 0.37, "y": -11.17, "curve": [0.133, 0.37, 0.335, -10.23, 0.133, -11.17, 0.335, 3.15]}, {"time": 0.5333, "x": -10.23, "y": 3.15, "curve": [0.71, -10.23, 0.883, 0.37, 0.71, 3.15, 0.883, -11.17]}, {"time": 1, "x": 0.37, "y": -11.17}]}, "front-shoulder": {"translate": [{"x": 1.46, "y": 10.15, "curve": [0.103, 1.46, 0.249, 1.36, 0.103, 10.15, 0.249, -4.39]}, {"time": 0.4, "x": 1.36, "y": -4.39, "curve": [0.621, 1.36, 0.85, 1.46, 0.621, -4.39, 0.85, 10.15]}, {"time": 1, "x": 1.46, "y": 10.15}]}, "back-shoulder": {"translate": [{"x": 1.4, "y": 0.44, "curve": [0.088, 1.4, 0.208, -2.47, 0.088, 0.44, 0.208, 8.61]}, {"time": 0.3333, "x": -2.47, "y": 8.61, "curve": [0.572, -2.47, 0.833, 1.4, 0.572, 8.61, 0.833, 0.44]}, {"time": 1, "x": 1.4, "y": 0.44}]}}, "transform": {"front-foot-board-transform": [{"mixRotate": 0.997}], "rear-foot-board-transform": [{}], "toes-board": [{"mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "attachments": {"default": {"front-foot": {"front-foot": {"deform": [{"offset": 26, "vertices": [-0.02832, -5.37024, -0.02832, -5.37024, 3.8188, -3.7757, -0.02832, -5.37024, -3.82159, 3.77847]}]}}, "front-shin": {"front-shin": {"deform": [{"offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -11.35158, -10.19225, -10.79865, -8.43765, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}, {"time": 0.3667, "offset": 14, "vertices": [0.5298, -1.12677, -11.66571, -9.07211, -25.65866, -17.53735, -25.53217, -16.50978, -11.78232, -11.26097, 0, 0, 0.60487, -1.63589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0, 0, -2.64522, -7.35739, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0.60487, -1.63589, 0.60487, -1.63589, 0.60487, -1.63589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0, 0, -10.06873, -12.0999]}, {"time": 0.5333, "offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -7.00775, -8.24771, -6.45482, -6.49312, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}, {"time": 1, "offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -11.35158, -10.19225, -10.79865, -8.43765, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}]}}, "hoverboard-board": {"hoverboard-board": {"deform": [{"curve": [0.067, 0, 0.2, 1]}, {"time": 0.2667, "offset": 1, "vertices": [2.45856, 0, 0, 0, 0, 0, 0, 0, 0, 3.55673, -0.0003, 3.55673, -0.0003, 0, 0, 0, 0, 0, 0, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, 0, 0, 0, 0, 0, 0, 0, 0, -4.90558, 0.11214, -9.40706, 0.00062, -6.34871, 0.00043, -6.34925, -6.57018, -6.34925, -6.57018, -6.34871, 0.00043, -2.3308, 0.00017, -2.33133, -6.57045, -2.33133, -6.57045, -2.3308, 0.00017, 0, 0, 0.00012, 2.45856, 0.00012, 2.45856, 0.00012, 2.45856, 0.00012, 2.45856, 3.3297, 4.44005, 3.3297, 4.44005, 3.3297, 4.44005, 0.00012, 2.45856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.46227, 0.00017, -2.46227, 0.00017, -2.52316, 1.1313, -2.52316, 1.1313, -2.52316, 1.1313, 0.00012, 2.45856, 0.00012, 2.45856, -9.40694, 2.45918, 1.88063, 0.44197, -0.00029, -3.54808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.52316, 1.1313, -2.52316, 1.1313, -2.52316, 1.1313, -2.46227, 0.00017, -2.46227, 0.00017, -2.46227, 0.00017, 0, 0, 0, 0, 0.00012, 2.45856], "curve": [0.45, 0, 0.817, 1]}, {"time": 1}]}}, "rear-foot": {"rear-foot": {"deform": [{"offset": 28, "vertices": [-1.93078, 1.34782, -0.31417, 2.33363, 3.05122, 0.33946, 2.31472, -2.01678, 2.17583, -2.05795, -0.04277, -2.99459, 1.15429, 0.26328, 0.97501, -0.67169]}]}}}}}, "idle": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-foot-target": {"translate": [{"x": -69.06}]}, "hip": {"rotate": [{"curve": [0.073, 0.35, 0.303, 1.27]}, {"time": 0.4, "value": 1.28, "curve": [0.615, 1.3, 0.847, -1.41]}, {"time": 1.2, "value": -1.38, "curve": [1.344, -1.37, 1.602, -0.28]}, {"time": 1.6667}], "translate": [{"x": -11.97, "y": -23.15, "curve": [0.059, -12.96, 0.258, -15.19, 0.142, -23.15, 0.341, -24.89]}, {"time": 0.4667, "x": -15.14, "y": -26.74, "curve": [0.62, -15.1, 0.788, -13.28, 0.597, -28.66, 0.75, -30.01]}, {"time": 0.9, "x": -12.02, "y": -30.01, "curve": [0.978, -11.13, 1.175, -9.05, 1.036, -29.94, 1.234, -28.08]}, {"time": 1.3333, "x": -9.06, "y": -26.64, "curve": [1.501, -9.06, 1.614, -10.95, 1.454, -24.89, 1.609, -23.15]}, {"time": 1.6667, "x": -11.97, "y": -23.15}]}, "rear-foot-target": {"translate": [{"x": 48.87}]}, "front-upper-arm": {"rotate": [{"value": -60.87, "curve": [0.154, -60.85, 0.452, -68.65]}, {"time": 0.8333, "value": -68.65, "curve": [1.221, -68.65, 1.542, -60.87]}, {"time": 1.6667, "value": -60.87}]}, "front-bracer": {"rotate": [{"value": 42.46, "curve": [0.029, 42.97, 0.134, 45.28]}, {"time": 0.3333, "value": 45.27, "curve": [0.578, 45.26, 0.798, 40.07]}, {"time": 0.8333, "value": 39.74, "curve": [0.878, 39.32, 1.019, 38.23]}, {"time": 1.2, "value": 38.22, "curve": [1.377, 38.22, 1.619, 41.68]}, {"time": 1.6667, "value": 42.46}]}, "rear-upper-arm": {"rotate": [{"value": 39.2, "curve": [0.185, 39.22, 0.5, 29.37]}, {"time": 0.6667, "value": 29.37, "curve": [0.917, 29.37, 1.417, 39.2]}, {"time": 1.6667, "value": 39.2}]}, "head": {"rotate": [{"value": -6.75, "curve": [0.176, -7.88, 0.349, -8.95]}, {"time": 0.4667, "value": -8.95, "curve": [0.55, -8.95, 0.697, -6.77]}, {"time": 0.8333, "value": -5.44, "curve": [0.88, -4.98, 1.05, -4.12]}, {"time": 1.1333, "value": -4.12, "curve": [1.266, -4.12, 1.469, -5.48]}, {"time": 1.6667, "value": -6.75}]}, "front-fist": {"rotate": [{"curve": [0.086, 0, 0.233, 2.48]}, {"time": 0.3333, "value": 4.13, "curve": [0.429, 5.7, 0.711, 10.06]}, {"time": 0.8333, "value": 10.06, "curve": [0.926, 10.06, 1.092, 4.21]}, {"time": 1.2, "value": 2.78, "curve": [1.349, 0.8, 1.551, 0]}, {"time": 1.6667}]}, "rear-bracer": {"rotate": [{"curve": [0.063, 0.54, 0.367, 3.39]}, {"time": 0.5333, "value": 3.39, "curve": [0.696, 3.39, 0.939, -1.63]}, {"time": 1.2, "value": -1.61, "curve": [1.42, -1.59, 1.574, -0.67]}, {"time": 1.6667}]}, "gun": {"rotate": [{"curve": [0.099, 0.27, 0.367, 1.23]}, {"time": 0.5333, "value": 1.23, "curve": [0.665, 1.23, 0.937, -0.56]}, {"time": 1.1333, "value": -0.55, "curve": [1.316, -0.55, 1.582, -0.21]}, {"time": 1.6667}]}, "torso": {"rotate": [{"value": -22.88, "curve": [0.099, -23.45, 0.363, -24.74]}, {"time": 0.5333, "value": -24.74, "curve": [0.706, -24.74, 0.961, -20.97]}, {"time": 1.1333, "value": -20.97, "curve": [1.355, -20.97, 1.567, -22.28]}, {"time": 1.6667, "value": -22.88}]}, "neck": {"rotate": [{"value": 3.78, "curve": [0.167, 3.78, 0.5, 5.45]}, {"time": 0.6667, "value": 5.45, "curve": [0.917, 5.45, 1.417, 3.78]}, {"time": 1.6667, "value": 3.78}]}, "torso2": {"rotate": [{"curve": [0.067, 0.33, 0.341, 2.54]}, {"time": 0.5333, "value": 2.54, "curve": [0.734, 2.55, 0.982, -0.94]}, {"time": 1.1333, "value": -0.93, "curve": [1.365, -0.91, 1.549, -0.56]}, {"time": 1.6667}]}, "torso3": {"rotate": [{"value": -2.15, "curve": [0.052, -1.9, 0.384, -0.15]}, {"time": 0.5333, "value": -0.14, "curve": [0.762, -0.13, 0.895, -3.1]}, {"time": 1.1333, "value": -3.1, "curve": [1.348, -3.1, 1.592, -2.46]}, {"time": 1.6667, "value": -2.15}]}, "hair1": {"rotate": [{"curve": [0.067, 0, 0.213, 2.86]}, {"time": 0.2667, "value": 3.65, "curve": [0.358, 4.99, 0.535, 7.92]}, {"time": 0.6667, "value": 7.92, "curve": [0.809, 7.92, 1.067, 5.49]}, {"time": 1.1333, "value": 4.7, "curve": [1.245, 3.34, 1.525, 0]}, {"time": 1.6667}]}, "hair2": {"rotate": [{"curve": [0.067, 0, 0.225, -7.97]}, {"time": 0.2667, "value": -9.75, "curve": [0.316, -11.84, 0.519, -16.66]}, {"time": 0.6667, "value": -16.66, "curve": [0.817, -16.66, 1.029, -11.43]}, {"time": 1.1333, "value": -9.14, "curve": [1.25, -6.56, 1.525, 0]}, {"time": 1.6667}]}, "hair3": {"rotate": [{"curve": [0.1, 0, 0.3, 1.32]}, {"time": 0.4, "value": 1.32, "curve": [0.55, 1.32, 0.866, 0.93]}, {"time": 1, "value": 0.73, "curve": [1.189, 0.46, 1.5, 0]}, {"time": 1.6667}]}, "hair4": {"rotate": [{"curve": [0.118, -0.44, 0.3, -8.52]}, {"time": 0.4, "value": -8.52, "curve": [0.55, -8.52, 0.85, 1.96]}, {"time": 1, "value": 1.96, "curve": [1.167, 1.96, 1.577, 0.38]}, {"time": 1.6667}]}, "head-control": {"translate": [{"curve": [0.098, 1.46, 0.3, 4.49, 0.17, 0.13, 0.316, -3.28]}, {"time": 0.4, "x": 4.55, "y": -5.95, "curve": [0.53, 4.64, 0.776, 2.59, 0.492, -8.89, 0.668, -14.21]}, {"time": 0.8667, "x": 1.42, "y": -14.26, "curve": [0.966, 0.15, 1.109, -2.91, 0.994, -14.26, 1.144, -10.58]}, {"time": 1.2333, "x": -3.02, "y": -8.26, "curve": [1.342, -3.02, 1.568, -1.48, 1.317, -6.1, 1.558, 0]}, {"time": 1.6667}]}, "front-shoulder": {"translate": [{"curve": [0.21, 0, 0.525, -1.72, 0.21, 0, 0.525, 4.08]}, {"time": 0.8333, "x": -1.72, "y": 4.08, "curve": [1.15, -1.72, 1.46, 0, 1.15, 4.08, 1.46, 0]}, {"time": 1.6667}]}}}, "idle-turn": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-upper-arm": {"rotate": [{"value": -302.77, "curve": [0, -406.9, 0.125, -420.87]}, {"time": 0.2667, "value": -420.87}], "translate": [{"x": 2.24, "y": -4.98, "curve": [0.067, 2.24, 0.111, 0, 0.067, -4.98, 0.111, 0]}, {"time": 0.2667}]}, "rear-upper-arm": {"rotate": [{"value": 248.56, "curve": [0, 371.28, 0.062, 399.2]}, {"time": 0.1333, "value": 399.2}], "translate": [{"x": -2.84, "y": 37.28, "curve": [0.033, -2.84, 0.069, 0, 0.033, 37.28, 0.069, 0]}, {"time": 0.1333}]}, "gun": {"rotate": [{"value": -3.95, "curve": [0, -10.4, 0.019, -20.43]}, {"time": 0.0333, "value": -20.45, "curve": [0.044, -20.47, 0.125, 0]}, {"time": 0.2}]}, "neck": {"rotate": [{"value": 17.2, "curve": [0, 6.27, 0.125, 3.78]}, {"time": 0.2667, "value": 3.78}]}, "hip": {"translate": [{"x": -2.69, "y": -6.79, "curve": [0.067, -2.69, 0.2, -11.97, 0.067, -6.79, 0.2, -23.15]}, {"time": 0.2667, "x": -11.97, "y": -23.15}]}, "front-fist": {"rotate": [{"value": -15.54, "curve": [0, -3.08, 0.034, 18.44]}, {"time": 0.0667, "value": 19.02, "curve": [0.108, 19.75, 0.169, 0]}, {"time": 0.2667}], "scale": [{"x": 0.94, "curve": [0, 0.962, 0.024, 1.237, 0, 1, 0.026, 0.947]}, {"time": 0.0667, "x": 1.236, "y": 0.947, "curve": [0.117, 1.235, 0.189, 1, 0.117, 0.947, 0.189, 1]}, {"time": 0.2667}]}, "rear-bracer": {"rotate": [{"value": 11.75, "curve": [0, -7.97, 0.017, -33.4]}, {"time": 0.0333, "value": -33.39, "curve": [0.049, -33.37, 0.131, 0]}, {"time": 0.2}]}, "torso": {"rotate": [{"value": -18.25, "curve": [0, -10.59, 0.125, -22.88]}, {"time": 0.2667, "value": -22.88}], "scale": [{"y": 1.03, "curve": [0.067, 1, 0.132, 1, 0.067, 1.03, 0.132, 1]}, {"time": 0.2667}]}, "head": {"rotate": [{"value": 5.12, "curve": [0, -6.34, 0.125, -6.75]}, {"time": 0.2667, "value": -6.75}], "scale": [{"y": 1.03, "curve": [0.067, 1, 0.107, 1, 0.067, 1.03, 0.107, 1]}, {"time": 0.2667}]}, "rear-foot-target": {"translate": [{"x": -58.39, "y": 30.48, "curve": [0, -7.15, 0.047, 16.62, 0, 12.71, 0.039, 0.22]}, {"time": 0.1, "x": 34.14, "y": -0.19, "curve": [0.136, 45.79, 0.163, 48.87, 0.133, -0.41, 0.163, 0]}, {"time": 0.2, "x": 48.87}]}, "front-bracer": {"rotate": [{"value": 6.69, "curve": [0, 19.76, 0.039, 56.53]}, {"time": 0.0667, "value": 56.63, "curve": [0.114, 56.79, 0.189, 42.46]}, {"time": 0.2667, "value": 42.46}]}, "front-foot-target": {"rotate": [{"value": -1.85, "curve": [0.014, -8.91, 0.047, -28.4]}, {"time": 0.1, "value": -28.89, "curve": [0.144, -29.29, 0.262, -21.77]}, {"time": 0.2667}], "translate": [{"x": 9.97, "y": 0.82, "curve": [0, -54.41, 0.078, -69.06, 0, 0.15, 0.078, 0]}, {"time": 0.1667, "x": -69.06}]}, "hair3": {"rotate": [{"value": -9.01, "curve": [0.044, -9.01, 0.072, 7.41]}, {"time": 0.1333, "value": 10.08, "curve": [0.166, 11.47, 0.208, 0]}, {"time": 0.2667}]}, "hair4": {"rotate": [{"value": -16.49, "curve": [0.044, -16.49, 0.101, -5.98]}, {"time": 0.1333, "value": -2.95, "curve": [0.162, -0.34, 0.208, 0]}, {"time": 0.2667}]}, "hair1": {"rotate": [{"value": -3.85, "curve": [0.044, -3.85, 0.072, 6.91]}, {"time": 0.1333, "value": 8.05, "curve": [0.166, 8.65, 0.208, 0]}, {"time": 0.2667}]}, "hair2": {"rotate": [{"value": 1.25, "curve": [0.044, 1.25, 0.072, 8.97]}, {"time": 0.1333, "value": 8.6, "curve": [0.166, 8.4, 0.208, 0]}, {"time": 0.2667}]}, "front-thigh": {"translate": [{"x": 12.21, "y": 1.89, "curve": [0.033, 12.21, 0.1, 0, 0.033, 1.89, 0.1, 0]}, {"time": 0.1333}]}, "rear-thigh": {"translate": [{"x": -16.11, "y": -1.38, "curve": [0.033, -16.11, 0.1, 0, 0.033, -1.38, 0.1, 0]}, {"time": 0.1333}]}, "torso3": {"rotate": [{"time": 0.2667, "value": -2.15}]}, "head-control": {"translate": [{"x": -13.72, "y": -34.7, "curve": [0.067, -13.72, 0.2, 0, 0.067, -34.7, 0.2, 0]}, {"time": 0.2667}]}, "front-shoulder": {"translate": [{"x": 1.13, "y": -14.31, "curve": [0.067, 1.13, 0.2, 0, 0.067, -14.31, 0.2, 0]}, {"time": 0.2667}]}}}, "jump": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}, {"time": 0.1, "name": "front-fist-closed"}, {"time": 0.8333, "name": "front-fist-open"}]}}, "bones": {"front-thigh": {"rotate": [{"value": 55.08, "curve": [0.007, 46.66, 0.043, 26.3]}, {"time": 0.0667, "value": 22.84, "curve": [0.1, 17.99, 0.165, 15.78]}, {"time": 0.2333, "value": 15.71, "curve": [0.309, 15.63, 0.408, 46.67]}, {"time": 0.5, "value": 63.6, "curve": [0.56, 74.72, 0.762, 91.48]}, {"time": 0.9667, "value": 91.81, "curve": [1.068, 92.01, 1.096, 22.05]}, {"time": 1.1667, "value": 22.25, "curve": [1.18, 22.29, 1.176, 56.17]}, {"time": 1.2, "value": 56.16, "curve": [1.246, 56.15, 1.263, 54.94]}, {"time": 1.3333, "value": 55.08}], "translate": [{"x": -5.13, "y": 11.55}]}, "torso": {"rotate": [{"value": -45.57, "curve": [0.022, -44.61, 0.03, -39.06]}, {"time": 0.0667, "value": -35.29, "curve": [0.12, -29.77, 0.28, -19.95]}, {"time": 0.4333, "value": -19.95, "curve": [0.673, -19.95, 0.871, -22.38]}, {"time": 0.9667, "value": -27.08, "curve": [1.094, -33.33, 1.176, -44.93]}, {"time": 1.3333, "value": -45.57}], "translate": [{"x": -3.79, "y": -0.77}]}, "rear-thigh": {"rotate": [{"value": 12.81, "curve": [0.067, 12.81, 0.242, 67.88]}, {"time": 0.2667, "value": 74.11, "curve": [0.314, 86.02, 0.454, 92.23]}, {"time": 0.5667, "value": 92.24, "curve": [0.753, 92.26, 0.966, 67.94]}, {"time": 1, "value": 61.32, "curve": [1.039, 53.75, 1.218, 12.68]}, {"time": 1.3333, "value": 12.81}]}, "rear-shin": {"rotate": [{"value": -115.64, "curve": [0.067, -117.17, 0.125, -117.15]}, {"time": 0.1667, "value": -117.15, "curve": [0.225, -117.15, 0.332, -108.76]}, {"time": 0.4, "value": -107.15, "curve": [0.48, -105.26, 0.685, -103.49]}, {"time": 0.7667, "value": -101.97, "curve": [0.826, -100.87, 0.919, -92.3]}, {"time": 1, "value": -92.28, "curve": [1.113, -92.26, 1.297, -114.22]}, {"time": 1.3333, "value": -115.64}]}, "front-upper-arm": {"rotate": [{"value": -40.21, "curve": [0.054, -35.46, 0.15, -31.12]}, {"time": 0.2, "value": -31.12, "curve": [0.308, -31.12, 0.547, -80.12]}, {"time": 0.6333, "value": -96.56, "curve": [0.697, -108.56, 0.797, -112.54]}, {"time": 0.8667, "value": -112.6, "curve": [1.137, -112.84, 1.274, -49.19]}, {"time": 1.3333, "value": -40.21}]}, "front-bracer": {"rotate": [{"value": 20.54, "curve": [0.054, 32.23, 0.192, 55.84]}, {"time": 0.2333, "value": 62.58, "curve": [0.29, 71.87, 0.375, 79.28]}, {"time": 0.4333, "value": 79.18, "curve": [0.555, 78.98, 0.684, 27.54]}, {"time": 0.7333, "value": 13.28, "curve": [0.786, -1.85, 0.874, -24.76]}, {"time": 1, "value": -25.45, "curve": [1.165, -26.36, 1.303, 9.1]}, {"time": 1.3333, "value": 20.54}]}, "front-fist": {"rotate": [{"value": -36.16, "curve": [0.114, -39.59, 0.3, -45.61]}, {"time": 0.4, "value": -45.61, "curve": [0.442, -45.61, 0.537, -21.54]}, {"time": 0.5667, "value": -15.4, "curve": [0.592, -10.23, 0.692, 11.89]}, {"time": 0.7333, "value": 11.73, "curve": [0.783, 11.54, 0.831, 1.8]}, {"time": 0.8667, "value": -5.78, "curve": [0.897, -12.22, 0.901, -14.22]}, {"time": 0.9333, "value": -14.51, "curve": [0.974, -14.89, 0.976, 10.38]}, {"time": 1, "value": 10.55, "curve": [1.027, 10.74, 1.023, -8.44]}, {"time": 1.0333, "value": -8.42, "curve": [1.059, -8.36, 1.074, 10.12]}, {"time": 1.1, "value": 10.22, "curve": [1.168, 10.48, 1.27, -36.07]}, {"time": 1.3333, "value": -36.16}]}, "rear-upper-arm": {"rotate": [{"value": 40.5, "curve": [0.048, 36.1, 0.168, 20.45]}, {"time": 0.3, "value": 20.45, "curve": [0.476, 20.45, 0.571, 33.76]}, {"time": 0.6, "value": 38.67, "curve": [0.642, 45.8, 0.681, 57.44]}, {"time": 0.7333, "value": 62.91, "curve": [0.829, 72.8, 0.996, 77.61]}, {"time": 1.0333, "value": 80.37, "curve": [1.082, 83.94, 1.148, 90.6]}, {"time": 1.2, "value": 90.6, "curve": [1.248, 90.46, 1.317, 53.07]}, {"time": 1.3333, "value": 49.06}]}, "rear-bracer": {"rotate": [{"value": 28.28, "curve": [0.022, 25.12, 0.187, -0.89]}, {"time": 0.2, "value": -2.52, "curve": [0.257, -9.92, 0.372, -17.38]}, {"time": 0.4333, "value": -17.41, "curve": [0.54, -17.47, 0.659, -16.91]}, {"time": 0.7667, "value": -12.1, "curve": [0.907, -5.79, 1.025, 14.58]}, {"time": 1.1, "value": 20.58, "curve": [1.191, 27.85, 1.283, 29.67]}, {"time": 1.3333, "value": 29.67}]}, "neck": {"rotate": [{"value": 11.88, "curve": [0.104, 11.82, 0.179, 11.15]}, {"time": 0.2, "value": 10.08, "curve": [0.255, 7.29, 0.405, -8.15]}, {"time": 0.4333, "value": -9.35, "curve": [0.508, -12.48, 0.595, -13.14]}, {"time": 0.6667, "value": -12.61, "curve": [0.714, -12.26, 0.815, -5.57]}, {"time": 0.8333, "value": -4.08, "curve": [0.883, -0.07, 1.045, 12.77]}, {"time": 1.1, "value": 15.06, "curve": [1.208, 19.6, 1.279, 20.64]}, {"time": 1.3333, "value": 20.73}]}, "head": {"rotate": [{"value": 13.14, "curve": [0.008, 12.19, 0.197, -23.53]}, {"time": 0.3333, "value": -23.95, "curve": [0.509, -23.95, 0.667, -2.66]}, {"time": 0.7333, "value": -2.66, "curve": [0.792, -2.66, 0.908, -13.32]}, {"time": 0.9667, "value": -13.32, "curve": [1.158, -13.11, 1.241, -1.58]}, {"time": 1.3333, "value": -1.58}], "scale": [{"curve": [0.041, 1, 0.052, 0.962, 0.041, 1, 0.052, 1.137]}, {"time": 0.1, "x": 0.954, "y": 1.137, "curve": [0.202, 0.962, 0.318, 1, 0.202, 1.137, 0.252, 1.002]}, {"time": 0.4667}, {"time": 1.0667, "x": 1.002, "curve": [1.092, 1.002, 1.126, 1.143, 1.092, 1, 1.128, 0.975]}, {"time": 1.1667, "x": 1.144, "y": 0.973, "curve": [1.204, 1.145, 1.233, 0.959, 1.206, 0.972, 1.227, 1.062]}, {"time": 1.2667, "x": 0.958, "y": 1.063, "curve": [1.284, 0.958, 1.292, 1.001, 1.288, 1.063, 1.288, 1.001]}, {"time": 1.3333}]}, "hip": {"translate": [{"y": -45.46, "curve": [0.042, -0.09, 0.15, 15.22, 0.031, 44.98, 0.123, 289.73]}, {"time": 0.2, "x": 15.22, "y": 415.85, "curve": [0.332, 15.22, 0.539, -34.52, 0.271, 532.93, 0.483, 720.5]}, {"time": 0.7667, "x": -34.52, "y": 721.6, "curve": [0.888, -34.52, 1.057, -21.95, 1.049, 721.17, 1.098, 379.84]}, {"time": 1.1333, "x": -15.67, "y": 266.77, "curve": [1.144, -14.77, 1.188, -10.53, 1.15, 213.72, 1.172, -61.32]}, {"time": 1.2333, "x": -6.53, "y": -61.34, "curve": [1.272, -3.22, 1.311, 0.05, 1.291, -61.36, 1.296, -44.8]}, {"time": 1.3333, "y": -45.46}]}, "front-shin": {"rotate": [{"value": -74.19, "curve": [0, -51.14, 0.042, -12.54]}, {"time": 0.1667, "value": -12.28, "curve": [0.285, -12.32, 0.37, -74.44]}, {"time": 0.4333, "value": -92.92, "curve": [0.498, -111.86, 0.617, -140.28]}, {"time": 0.9, "value": -140.84, "curve": [1.004, -141.04, 1.09, -47.87]}, {"time": 1.1, "value": -37.44, "curve": [1.108, -29.83, 1.14, -21.18]}, {"time": 1.1667, "value": -21.08, "curve": [1.18, -21.03, 1.191, -50.65]}, {"time": 1.2, "value": -53.17, "curve": [1.22, -58.53, 1.271, -73.38]}, {"time": 1.3333, "value": -74.19}]}, "front-foot": {"rotate": [{"value": 7.35, "curve": [0, 4.8, 0.05, -26.64]}, {"time": 0.0667, "value": -26.64, "curve": [0.192, -26.64, 0.442, -11.77]}, {"time": 0.5667, "value": -11.77, "curve": [0.692, -11.77, 0.942, -19.36]}, {"time": 1.0667, "value": -19.36, "curve": [1.133, -19.36, 1.32, 3.82]}, {"time": 1.3333, "value": 7.35}]}, "rear-foot": {"rotate": [{"value": -7.14}]}, "gun": {"rotate": [{"value": 12.36, "curve": [0.022, 16.28, 0.15, 30.81]}, {"time": 0.2, "value": 30.81, "curve": [0.258, 30.81, 0.375, 13.26]}, {"time": 0.4333, "value": 13.26, "curve": [0.508, 13.26, 0.658, 15.05]}, {"time": 0.7333, "value": 14.98, "curve": [0.789, 14.94, 0.828, 13.62]}, {"time": 0.8667, "value": 12.72, "curve": [0.887, 12.25, 0.984, 9.83]}, {"time": 1.0333, "value": 8.6, "curve": [1.045, 8.31, 1.083, 7.55]}, {"time": 1.1333, "value": 7.13, "curve": [1.175, 6.78, 1.283, 6.18]}, {"time": 1.3333, "value": 6.18}]}, "front-leg-target": {"translate": [{"x": -13.95, "y": -30.34}]}, "rear-leg-target": {"rotate": [{"value": -38.43}], "translate": [{"x": 85, "y": -33.59}]}, "front-foot-target": {"rotate": [{"value": -62.54}], "translate": [{"x": 16.34, "y": 0.18}]}, "rear-foot-target": {"rotate": [{"value": 18.55}], "translate": [{"x": -176.39, "y": 134.12}]}, "back-foot-tip": {"rotate": [{"value": -143.73, "curve": [0.083, -144.24, 0.167, -74.26]}, {"time": 0.2667, "value": -52.76, "curve": [0.342, -36.57, 0.513, -36.57]}, {"time": 0.6333, "value": -30.97, "curve": [0.724, -26.78, 0.848, -17.06]}, {"time": 0.9667, "value": -16.74, "curve": [1.167, -16.2, 1.272, -144.17]}, {"time": 1.3333, "value": -143.73}]}, "front-foot-tip": {"rotate": [{"value": -1.57, "curve": [0, -24.71, 0.162, -60.88]}, {"time": 0.2667, "value": -60.83, "curve": [0.342, -60.8, 0.582, -43.5]}, {"time": 0.7, "value": -39.45, "curve": [0.773, -36.94, 0.832, -36.78]}, {"time": 0.9667, "value": -36.6, "curve": [1.054, -36.49, 1.092, -37.37]}, {"time": 1.1667, "value": -33.26, "curve": [1.237, -29.37, 1.147, -1.41]}, {"time": 1.2, "value": -1.57}]}, "hair3": {"rotate": [{"value": -6.81, "curve": [0, 13.59, 0.117, 18.21]}, {"time": 0.1333, "value": 18.21, "curve": [0.167, 18.21, 0.26, 12.95]}, {"time": 0.3, "value": 11.56, "curve": [0.382, 8.7, 0.55, 9.43]}, {"time": 0.6667, "value": 9.32, "curve": [0.843, 9.15, 0.918, -7.34]}, {"time": 1.3333, "value": -6.81}], "translate": [{"time": 0.6667, "curve": [0.781, 0, 0.972, 16.03, 0.781, 0, 0.972, 0.92]}, {"time": 1.1333, "x": 16.03, "y": 0.92, "curve": [1.211, 16.03, 1.281, 0, 1.211, 0.92, 1.281, 0]}, {"time": 1.3333}]}, "hair4": {"rotate": [{"value": -6.81, "curve": [0.001, -3.88, 0.063, 16.18]}, {"time": 0.1667, "value": 16.14, "curve": [0.242, 16.1, 0.249, 16.07]}, {"time": 0.3333, "value": 13.46, "curve": [0.442, 10.09, 0.573, -2.2]}, {"time": 0.6, "value": -6.04, "curve": [0.614, -8.05, 0.717, -33.44]}, {"time": 0.7667, "value": -33.44, "curve": [0.809, -33.44, 0.835, -31.32]}, {"time": 0.8667, "value": -27.36, "curve": [0.874, -26.47, 0.903, -14.28]}, {"time": 0.9333, "value": -14.47, "curve": [0.956, -14.62, 0.944, -25.91]}, {"time": 1, "value": -25.96, "curve": [1.062, -26.02, 1.051, -1.87]}, {"time": 1.0667, "value": -1.87, "curve": [1.096, -1.87, 1.096, -16.09]}, {"time": 1.1333, "value": -16.08, "curve": [1.169, -16.08, 1.153, -3.38]}, {"time": 1.2, "value": -3.38, "curve": [1.234, -3.38, 1.271, -6.07]}, {"time": 1.3333, "value": -6.07}]}, "hair2": {"rotate": [{"value": -6.81, "curve": [0, -3.17, 0.042, 16.33]}, {"time": 0.0667, "value": 16.33, "curve": [0.21, 15.74, 0.208, -12.06]}, {"time": 0.3333, "value": -12.21, "curve": [0.417, -12.3, 0.552, -3.98]}, {"time": 0.6667, "value": 1.52, "curve": [0.726, 4.35, 0.817, 4.99]}, {"time": 0.8667, "value": 4.99, "curve": [0.901, 4.99, 0.912, -29.05]}, {"time": 0.9667, "value": -27.45, "curve": [0.987, -26.83, 1.018, -5.42]}, {"time": 1.0667, "value": -5.46, "curve": [1.107, -5.22, 1.095, -33.51]}, {"time": 1.1333, "value": -33.28, "curve": [1.162, -33.57, 1.192, 8.04]}, {"time": 1.2667, "value": 7.86, "curve": [1.302, 7.77, 1.313, 2.7]}, {"time": 1.3333, "value": 2.7}]}, "hair1": {"rotate": [{"value": -6.81, "curve": [0.001, -3.12, 0.074, 14.66]}, {"time": 0.1333, "value": 14.66, "curve": [0.188, 14.8, 0.293, 9.56]}, {"time": 0.3333, "value": 5.99, "curve": [0.381, 1.72, 0.55, -11.11]}, {"time": 0.6667, "value": -11.11, "curve": [0.833, -11.11, 0.933, 22.54]}, {"time": 1.1, "value": 22.54, "curve": [1.158, 22.54, 1.275, -6.81]}, {"time": 1.3333, "value": -6.81}]}, "torso2": {"rotate": [{"value": 4.52, "curve": [0.013, 2.33, 0.092, -9.75]}, {"time": 0.1333, "value": -9.75, "curve": [0.175, -9.75, 0.291, -1.26]}, {"time": 0.3333, "value": 0.96, "curve": [0.359, 2.3, 0.543, 4.25]}, {"time": 0.6, "value": 4.68, "curve": [0.683, 5.3, 0.771, 5.92]}, {"time": 0.8333, "value": 6.48, "curve": [0.871, 6.82, 1.083, 11.37]}, {"time": 1.1667, "value": 11.37, "curve": [1.208, 11.37, 1.317, 6.18]}, {"time": 1.3333, "value": 4.52}], "translate": [{"curve": [0, 0, 0.082, -2.24, 0, 0, 0.082, -0.42]}, {"time": 0.1667, "x": -2.98, "y": -0.56, "curve": [0.232, -2.24, 0.298, 0, 0.232, -0.42, 0.298, 0]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 0, 0.912, 0.26, 0.889, 0, 0.912, 0.06]}, {"time": 0.9333, "x": 0.68, "y": 0.23, "curve": [1.016, 2.22, 1.095, 5.9, 1.023, 0.97, 1.095, 1.99]}, {"time": 1.1667, "x": 6.47, "y": 2.18, "curve": [1.23, 5.75, 1.286, 0, 1.23, 1.94, 1.286, 0]}, {"time": 1.3333}]}, "torso3": {"rotate": [{"value": 4.52, "curve": [0.025, 4.52, 0.075, -6.17]}, {"time": 0.1, "value": -6.17, "curve": [0.175, -6.17, 0.381, -0.71]}, {"time": 0.4, "value": -0.25, "curve": [0.447, 0.87, 0.775, 4.84]}, {"time": 0.9, "value": 4.84, "curve": [1.008, 4.84, 1.225, 4.52]}, {"time": 1.3333, "value": 4.52}]}, "head-control": {"translate": [{"curve": [0.138, -2.4, 0.227, -10.44, 0.123, 1.05, 0.227, 2.7]}, {"time": 0.3667, "x": -10.44, "y": 2.7, "curve": [0.484, -10.44, 0.585, -5.63, 0.484, 2.7, 0.629, -23.62]}, {"time": 0.7333, "x": -2.29, "y": -26.61, "curve": [0.818, -0.39, 0.962, 1.21, 0.858, -30.17, 0.972, -28.75]}, {"time": 1.1, "x": 1.25, "y": -28.75, "curve": [1.192, 1.28, 1.234, 0.98, 1.224, -28.75, 1.235, -2.15]}, {"time": 1.3333}]}, "front-shoulder": {"translate": [{"curve": [0.031, -2.22, 0.065, -3.73, 0.02, -3.25, 0.065, -14.74]}, {"time": 0.1, "x": -3.73, "y": -14.74, "curve": [0.216, -3.73, 0.384, -0.17, 0.216, -14.74, 0.402, -12.51]}, {"time": 0.5, "x": 1.63, "y": -9.51, "curve": [0.632, 3.69, 0.935, 7.41, 0.585, -6.91, 0.909, 10.86]}, {"time": 1.1, "x": 7.45, "y": 10.99, "curve": [1.18, 7.46, 1.265, 2.86, 1.193, 11.05, 1.294, 3.38]}, {"time": 1.3333}]}}, "ik": {"front-foot-ik": [{"mix": 0, "curve": [0.3, 0, 0.9, 1, 0.3, 0, 0.9, 0]}, {"time": 1.2}], "front-leg-ik": [{"mix": 0, "bendPositive": false, "curve": [0.3, 0, 0.9, 1, 0.3, 0, 0.9, 0]}, {"time": 1.2, "bendPositive": false}], "rear-foot-ik": [{"mix": 0}], "rear-leg-ik": [{"mix": 0, "bendPositive": false}]}, "events": [{"time": 1.2, "name": "footstep"}]}, "portal": {"slots": {"clipping": {"attachment": [{"name": "clipping"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "mouth": {"attachment": [{"time": 0.9, "name": "mouth-grind"}, {"time": 2.2667, "name": "mouth-smile"}]}, "portal-bg": {"attachment": [{"name": "portal-bg"}, {"time": 3}]}, "portal-flare1": {"attachment": [{"time": 1.1, "name": "portal-flare1"}, {"time": 1.1333, "name": "portal-flare2"}, {"time": 1.1667, "name": "portal-flare3"}, {"time": 1.2, "name": "portal-flare1"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3333}]}, "portal-flare2": {"attachment": [{"time": 1.1, "name": "portal-flare2"}, {"time": 1.1333, "name": "portal-flare3"}, {"time": 1.1667, "name": "portal-flare1"}, {"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667}]}, "portal-flare3": {"attachment": [{"time": 1.2, "name": "portal-flare3"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667}]}, "portal-flare4": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare1"}, {"time": 1.2667, "name": "portal-flare2"}, {"time": 1.3333}]}, "portal-flare5": {"attachment": [{"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3333}]}, "portal-flare6": {"attachment": [{"time": 1.2667, "name": "portal-flare3"}, {"time": 1.3333}]}, "portal-flare7": {"attachment": [{"time": 1.1333, "name": "portal-flare2"}, {"time": 1.1667}]}, "portal-flare8": {"attachment": [{"time": 1.2, "name": "portal-flare3"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667}]}, "portal-flare9": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3}]}, "portal-flare10": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare1"}, {"time": 1.2667, "name": "portal-flare3"}, {"time": 1.3}]}, "portal-shade": {"attachment": [{"name": "portal-shade"}, {"time": 3}]}, "portal-streaks1": {"attachment": [{"name": "portal-streaks1"}, {"time": 3}]}, "portal-streaks2": {"attachment": [{"name": "portal-streaks2"}, {"time": 3}]}}, "bones": {"portal-root": {"translate": [{"x": -458.35, "y": 105.19, "curve": [0.333, -458.22, 0.669, -457.86, 0.934, 105.19, 0.671, 105.19]}, {"time": 1, "x": -456.02, "y": 105.19, "curve": [1.339, -454.14, 2.208, -447.28, 1.35, 105.19, 2.05, 105.19]}, {"time": 2.4, "x": -439.12, "y": 105.19, "curve": [2.463, -436.44, 2.502, -432.92, 2.487, 105.19, 2.512, 105.09]}, {"time": 2.6, "x": -432.58, "y": 105.09, "curve": [2.784, -431.94, 2.978, -446.6, 2.772, 105.09, 2.933, 105.19]}, {"time": 3.0333, "x": -457.42, "y": 105.19}], "scale": [{"x": 0.003, "y": 0.006, "curve": [0.329, 0.044, 0.347, 0.117, 0.329, 0.097, 0.37, 0.249]}, {"time": 0.4, "x": 0.175, "y": 0.387, "curve": [0.63, 0.619, 0.663, 0.723, 0.609, 1.338, 0.645, 1.524]}, {"time": 0.7333, "x": 0.724, "y": 1.52, "curve": [0.798, 0.725, 0.907, 0.647, 0.797, 1.517, 0.895, 1.424]}, {"time": 1, "x": 0.645, "y": 1.426, "curve": [1.095, 0.643, 1.139, 0.688, 1.089, 1.428, 1.115, 1.513]}, {"time": 1.2333, "x": 0.685, "y": 1.516, "curve": [1.325, 0.683, 1.508, 0.636, 1.343, 1.518, 1.467, 1.4]}, {"time": 1.6, "x": 0.634, "y": 1.401, "curve": [1.728, 0.631, 1.946, 0.687, 1.722, 1.402, 1.924, 1.522]}, {"time": 2.0667, "x": 0.688, "y": 1.522, "curve": [2.189, 0.69, 2.289, 0.649, 2.142, 1.522, 2.265, 1.417]}, {"time": 2.4, "x": 0.65, "y": 1.426, "curve": [2.494, 0.651, 2.504, 0.766, 2.508, 1.434, 2.543, 1.566]}, {"time": 2.6, "x": 0.766, "y": 1.568, "curve": [2.73, 0.765, 3.006, 0.098, 2.767, 1.564, 2.997, 0.1]}, {"time": 3.0333, "x": 0.007, "y": 0.015}]}, "portal-streaks1": {"rotate": [{}, {"time": 3.1667, "value": 1200}], "translate": [{"x": 15.15, "curve": [0.162, 15.15, 0.432, 12.6, 0.162, 0, 0.432, -3.86]}, {"time": 0.6667, "x": 10.9, "y": -6.44, "curve": [0.794, 9.93, 0.912, 9.21, 0.794, -7.71, 0.912, -8.66]}, {"time": 1, "x": 9.21, "y": -8.66, "curve": [1.083, 9.21, 1.25, 21.53, 1.083, -8.66, 1.265, -4.9]}, {"time": 1.3333, "x": 21.53, "y": -3.19, "curve": [1.5, 21.53, 1.939, 12.3, 1.446, -0.37, 1.9, 6.26]}, {"time": 2.0667, "x": 11.26, "y": 6.26, "curve": [2.239, 9.85, 2.389, 9.68, 2.208, 6.26, 2.523, 0.51]}, {"time": 2.5667, "x": 9.39, "y": -0.8, "curve": [2.657, 9.24, 2.842, 9.21, 2.646, -3.2, 2.842, -8.91]}, {"time": 2.9333, "x": 9.21, "y": -8.91}], "scale": [{"curve": [0.167, 1, 0.5, 1.053, 0.167, 1, 0.5, 1.053]}, {"time": 0.6667, "x": 1.053, "y": 1.053, "curve": [0.833, 1.053, 1.167, 0.986, 0.833, 1.053, 1.167, 0.986]}, {"time": 1.3333, "x": 0.986, "y": 0.986, "curve": [1.5, 0.986, 1.833, 1.053, 1.5, 0.986, 1.833, 1.053]}, {"time": 2, "x": 1.053, "y": 1.053}]}, "portal-streaks2": {"rotate": [{}, {"time": 3.1667, "value": 600}], "translate": [{"x": -2.11}, {"time": 1, "x": -2.11, "y": 6.63}, {"time": 1.9333, "x": -2.11}], "scale": [{"x": 1.014, "y": 1.014, "curve": [0.229, 0.909, 0.501, 0.755, 0.242, 0.892, 0.502, 0.768]}, {"time": 0.8667, "x": 0.745, "y": 0.745, "curve": [1.282, 0.733, 2.021, 0.699, 1.27, 0.719, 2.071, 0.709]}, {"time": 2.2, "x": 0.7, "y": 0.704, "curve": [2.315, 0.7, 2.421, 0.794, 2.311, 0.701, 2.485, 0.797]}, {"time": 2.5667, "x": 0.794, "y": 0.794, "curve": [2.734, 0.794, 2.99, 0.323, 2.714, 0.789, 3.019, 0.341]}, {"time": 3.1667, "x": 0, "y": 0}]}, "portal-shade": {"translate": [{"x": -29.68}], "scale": [{"x": 0.714, "y": 0.714}]}, "portal": {"rotate": [{}, {"time": 3.1667, "value": 600}]}, "clipping": {"translate": [{"x": -476.55, "y": 2.27}], "scale": [{"x": 0.983, "y": 1.197}]}, "hip": {"rotate": [{"time": 1.0667, "value": 22.74, "curve": [1.163, 18.84, 1.77, 8.77]}, {"time": 1.9, "value": 7.82, "curve": [2.271, 5.1, 2.89, 0]}, {"time": 3.1667}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -694.16, "y": 183.28, "curve": [1.091, -602.08, 1.138, -427.59, 1.115, 185.6, 1.171, 133.18]}, {"time": 1.2333, "x": -316.97, "y": 55.29, "curve": [1.317, -220.27, 1.512, -123.21, 1.271, 8.68, 1.461, -83.18]}, {"time": 1.6, "x": -95.53, "y": -112.23, "curve": [1.718, -58.25, 2.037, -22.54, 1.858, -166.17, 2.109, -31.4]}, {"time": 2.1667, "x": -14.82, "y": -31.12, "curve": [2.294, -7.28, 2.442, -7.2, 2.274, -30.6, 2.393, -36.76]}, {"time": 2.6, "x": -7.2, "y": -36.96, "curve": [2.854, -7.2, 3.071, -11.87, 2.786, -36.27, 3.082, -22.98]}, {"time": 3.1667, "x": -11.97, "y": -23.15}]}, "rear-foot-target": {"rotate": [{"time": 1.0667, "value": 41.6, "curve": "stepped"}, {"time": 1.2333, "value": 41.6, "curve": [1.258, 41.6, 1.379, 35.46]}, {"time": 1.4, "value": 30.09, "curve": [1.412, 27.04, 1.433, 10.65]}, {"time": 1.4333, "value": -0.28}, {"time": 1.6, "value": 2.44}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -591.13, "y": 438.46, "curve": [1.076, -539.77, 1.206, -268.1, 1.117, 418.44, 1.21, 333.18]}, {"time": 1.2333, "x": -225.28, "y": 304.53, "curve": [1.265, -175.22, 1.393, -74.21, 1.296, 226.52, 1.401, 49.61]}, {"time": 1.4333, "x": -52.32, "y": 0.2, "curve": [1.454, -40.85, 1.616, 40.87, 1.466, 0.17, 1.614, 0.04]}, {"time": 1.6667, "x": 45.87, "y": 0.01}, {"time": 1.9333, "x": 48.87}]}, "front-foot-target": {"rotate": [{"time": 1.0667, "value": 32.08, "curve": [1.108, 32.08, 1.192, 35.16]}, {"time": 1.2333, "value": 35.16, "curve": [1.258, 35.16, 1.317, 2.23]}, {"time": 1.3333, "value": -4.74, "curve": [1.351, -12.14, 1.429, -34.96]}, {"time": 1.6, "value": -34.77, "curve": [1.765, -34.58, 1.897, -17.25]}, {"time": 1.9333}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -533.93, "y": 363.75, "curve": [1.074, -480.85, 1.18, -261.31, 1.094, 362.3, 1.195, 267.77]}, {"time": 1.2333, "x": -201.23, "y": 199.93, "curve": [1.269, -161.38, 1.294, -140.32, 1.274, 126.67, 1.308, 77.12]}, {"time": 1.3333, "x": -124.08, "y": 0.2, "curve": [1.426, -85.6, 1.633, -69.06, 1.45, 0.48, 1.633, 0]}, {"time": 1.7333, "x": -69.06}]}, "torso": {"rotate": [{"time": 1.0667, "value": 27.02, "curve": [1.187, 26.86, 1.291, 7.81]}, {"time": 1.3333, "value": -2.62, "curve": [1.402, -19.72, 1.429, -48.64]}, {"time": 1.4667, "value": -56.31, "curve": [1.509, -64.87, 1.62, -77.14]}, {"time": 1.7333, "value": -77.34, "curve": [1.837, -76.89, 1.895, -71.32]}, {"time": 2, "value": -57.52, "curve": [2.104, -43.83, 2.189, -28.59]}, {"time": 2.3, "value": -29.03, "curve": [2.413, -29.48, 2.513, -36.79]}, {"time": 2.6667, "value": -36.79, "curve": [2.814, -36.95, 2.947, -22.88]}, {"time": 3.1667, "value": -22.88}]}, "neck": {"rotate": [{"time": 1.0667, "value": -3.57, "curve": [1.146, -3.66, 1.15, -13.5]}, {"time": 1.2333, "value": -13.5, "curve": [1.428, -13.5, 1.443, 11.58]}, {"time": 1.5667, "value": 11.42, "curve": [1.658, 11.3, 1.775, 3.78]}, {"time": 1.8667, "value": 3.78, "curve": [1.92, 3.78, 2.036, 8.01]}, {"time": 2.1, "value": 7.93, "curve": [2.266, 7.72, 2.42, 3.86]}, {"time": 2.5333, "value": 3.86, "curve": [2.783, 3.86, 3.004, 3.78]}, {"time": 3.1667, "value": 3.78}]}, "head": {"rotate": [{"time": 1.0667, "value": 16.4, "curve": [1.133, 9.9, 1.207, 1.87]}, {"time": 1.3333, "value": 1.67, "curve": [1.46, 1.56, 1.547, 47.54]}, {"time": 1.7333, "value": 47.55, "curve": [1.897, 47.56, 2.042, 5.68]}, {"time": 2.0667, "value": 0.86, "curve": [2.074, -0.61, 2.086, -2.81]}, {"time": 2.1, "value": -5.31, "curve": [2.145, -13.07, 2.216, -23.65]}, {"time": 2.2667, "value": -23.71, "curve": [2.334, -23.79, 2.426, -13.43]}, {"time": 2.4667, "value": -9.18, "curve": [2.498, -5.91, 2.604, 2.53]}, {"time": 2.6667, "value": 2.52, "curve": [2.738, 2.24, 2.85, -8.76]}, {"time": 2.9333, "value": -8.67, "curve": [3.036, -8.55, 3.09, -7.09]}, {"time": 3.1667, "value": -6.75}], "scale": [{"time": 1.3333, "curve": [1.392, 1, 1.526, 1, 1.392, 1, 1.508, 1.043]}, {"time": 1.5667, "x": 0.992, "y": 1.043, "curve": [1.598, 0.985, 1.676, 0.955, 1.584, 1.043, 1.672, 1.04]}, {"time": 1.7333, "x": 0.954, "y": 1.029, "curve": [1.843, 0.954, 1.933, 1, 1.825, 1.013, 1.933, 1]}, {"time": 2}]}, "rear-upper-arm": {"rotate": [{"time": 0.9, "value": 39.24, "curve": [0.968, 39.93, 1.267, 85.31]}, {"time": 1.4667, "value": 112.27, "curve": [1.555, 124.24, 1.576, 126.44]}, {"time": 1.6333, "value": 126.44, "curve": [1.782, 126.44, 1.992, 94.55]}, {"time": 2.1, "value": 79.96, "curve": [2.216, 64.26, 2.407, 34.36]}, {"time": 2.5667, "value": 33.38, "curve": [2.815, 31.87, 3.1, 39.2]}, {"time": 3.1667, "value": 39.2}]}, "back-foot-tip": {"rotate": [{"time": 1.0667, "value": 56.07, "curve": [1.138, 59.21, 1.192, 59.65]}, {"time": 1.2333, "value": 59.46, "curve": [1.295, 59.17, 1.45, 22.54]}, {"time": 1.4667, "value": -0.84}]}, "front-upper-arm": {"rotate": [{"time": 1.0667, "value": 118.03, "curve": [1.075, 93.64, 1.358, -34.03]}, {"time": 1.6667, "value": -33.94, "curve": [1.808, -33.89, 1.879, -25]}, {"time": 1.9667, "value": -25.19, "curve": [2.09, -25.46, 2.312, -34.58]}, {"time": 2.3667, "value": -38.36, "curve": [2.465, -45.18, 2.557, -60.1]}, {"time": 2.8333, "value": -61.1, "curve": [2.843, -61.06, 3.16, -60.87]}, {"time": 3.1667, "value": -60.87}]}, "front-bracer": {"rotate": [{"time": 1.0667, "value": 0.66, "curve": [1.108, 0.66, 1.221, 44.95]}, {"time": 1.2333, "value": 49.25, "curve": [1.263, 59.42, 1.342, 68.06]}, {"time": 1.3667, "value": 68.34, "curve": [1.409, 68.8, 1.476, 4.9]}, {"time": 1.5, "value": -2.05, "curve": [1.529, -10.3, 1.695, -15.95]}, {"time": 1.7333, "value": -17.38, "curve": [1.807, -20.1, 1.878, -21.19]}, {"time": 1.9333, "value": -21.08, "curve": [2.073, -20.8, 2.146, -7.63]}, {"time": 2.1667, "value": -3.64, "curve": [2.186, 0.12, 2.275, 15.28]}, {"time": 2.3333, "value": 21.78, "curve": [2.392, 28.31, 2.575, 37.66]}, {"time": 2.7, "value": 39.43, "curve": [2.947, 42.93, 3.02, 42.46]}, {"time": 3.1667, "value": 42.46}]}, "front-thigh": {"translate": [{"time": 1.1, "x": -6.41, "y": 18.23, "curve": "stepped"}, {"time": 1.1333, "x": -6.41, "y": 18.23}, {"time": 1.2, "x": 1.61, "y": 3.66}, {"time": 1.2333, "x": 4.5, "y": -3.15}, {"time": 1.3667, "x": -3.79, "y": 2.94}, {"time": 1.4, "x": -8.37, "y": 8.72}, {"time": 1.4333, "x": -11.26, "y": 16.99}, {"time": 1.4667, "x": -9.89, "y": 24.73, "curve": "stepped"}, {"time": 1.8667, "x": -9.89, "y": 24.73}, {"time": 2.1}]}, "front-foot-tip": {"rotate": [{"time": 1.0667, "value": 42.55, "curve": "stepped"}, {"time": 1.1333, "value": 42.55}, {"time": 1.2333, "value": 17.71}, {"time": 1.3667, "value": 3.63}, {"time": 1.4333}]}, "rear-bracer": {"rotate": [{"time": 1.0667, "value": 108.71, "curve": [1.082, 108.29, 1.437, 50.73]}, {"time": 1.5667, "value": 24.87, "curve": [1.62, 14.2, 1.66, -11.74]}, {"time": 1.7333, "value": -11.74, "curve": [1.961, -11.73, 2.172, 1.66]}, {"time": 2.2667, "value": 7.88, "curve": [2.331, 12.13, 2.439, 18.65]}, {"time": 2.5333, "value": 18.72, "curve": [2.788, 18.91, 3.145, -0.3]}, {"time": 3.1667}]}, "front-fist": {"rotate": [{"time": 1.1, "value": 6.32, "curve": [1.11, 3.31, 1.153, -5.07]}, {"time": 1.2333, "value": -5.13, "curve": [1.311, -5.19, 1.364, 34.65]}, {"time": 1.4667, "value": 34.53, "curve": [1.574, 34.41, 1.547, -55.78]}, {"time": 1.8667, "value": -54.7, "curve": [1.947, -54.7, 2.03, -53.94]}, {"time": 2.1333, "value": -42.44, "curve": [2.215, -33.42, 2.358, -4.43]}, {"time": 2.4, "value": 0.03, "curve": [2.444, 4.66, 2.536, 8.2]}, {"time": 2.6333, "value": 8.2, "curve": [2.733, 8.19, 2.804, -0.67]}, {"time": 2.9, "value": -0.82, "curve": [3.127, -1.16, 3.093, 0]}, {"time": 3.1667}]}, "gun": {"rotate": [{"time": 1.2667, "curve": [1.35, 0, 1.549, 7.49]}, {"time": 1.6, "value": 9.5, "curve": [1.663, 12.02, 1.846, 19.58]}, {"time": 1.9333, "value": 19.43, "curve": [1.985, 19.4, 2.057, 2.98]}, {"time": 2.2, "value": 2.95, "curve": [2.304, 3.55, 2.458, 10.8]}, {"time": 2.5, "value": 10.8, "curve": [2.642, 10.8, 2.873, -2.54]}, {"time": 2.9333, "value": -2.55, "curve": [3.09, -2.57, 3.08, 0]}, {"time": 3.1667}]}, "hair2": {"rotate": [{"time": 1.0667, "value": 26.19, "curve": [1.158, 26.19, 1.368, 26]}, {"time": 1.4333, "value": 24.43, "curve": [1.534, 22.03, 2, -29.14]}, {"time": 2.2, "value": -29.14, "curve": [2.292, -29.14, 2.475, 6.71]}, {"time": 2.5667, "value": 6.71, "curve": [2.675, 6.71, 2.814, -5.06]}, {"time": 2.9, "value": -5.06, "curve": [2.973, -5.06, 3.123, 0]}, {"time": 3.1667}]}, "hair4": {"rotate": [{"time": 1.0667, "value": 5.21, "curve": [1.108, 5.21, 1.192, 26.19]}, {"time": 1.2333, "value": 26.19, "curve": [1.317, 26.19, 1.483, 10.63]}, {"time": 1.5667, "value": 10.63, "curve": [1.627, 10.63, 1.642, 17.91]}, {"time": 1.7, "value": 17.94, "curve": [1.761, 17.97, 1.774, 8.22]}, {"time": 1.8, "value": 3.33, "curve": [1.839, -4.21, 1.95, -22.67]}, {"time": 2, "value": -22.67, "curve": [2.025, -22.67, 2.123, -21.86]}, {"time": 2.1667, "value": -18.71, "curve": [2.228, -14.31, 2.294, -0.3]}, {"time": 2.3667, "value": 6.36, "curve": [2.433, 12.45, 2.494, 19.21]}, {"time": 2.6, "value": 19.21, "curve": [2.729, 19.21, 2.854, 6.75]}, {"time": 2.9333, "value": 4.62, "curve": [3.09, 0.45, 3.062, 0]}, {"time": 3.1667}]}, "hair3": {"rotate": [{"time": 1.4333, "curve": [1.45, 0, 1.452, 11.29]}, {"time": 1.5, "value": 11.21, "curve": [1.596, 11.06, 1.573, -14.17]}, {"time": 1.7333, "value": -20.4, "curve": [1.851, -24.98, 1.943, -28.45]}, {"time": 2.2, "value": -28.75, "curve": [2.317, -28.75, 2.55, 7.04]}, {"time": 2.6667, "value": 7.04, "curve": [2.792, 7.04, 2.885, -5.19]}, {"time": 2.9667, "value": -5.19, "curve": [3.037, -5.19, 3.096, 0]}, {"time": 3.1667}]}, "hair1": {"rotate": [{"time": 1.2333, "curve": [1.283, 0, 1.349, 3.99]}, {"time": 1.4333, "value": 6.58, "curve": [1.497, 8.54, 1.683, 9.35]}, {"time": 1.7667, "value": 9.35, "curve": [1.825, 9.35, 1.945, -8.71]}, {"time": 2, "value": -11.15, "curve": [2.058, -13.71, 2.2, -14.97]}, {"time": 2.2667, "value": -14.97, "curve": [2.367, -14.97, 2.567, 18.77]}, {"time": 2.6667, "value": 18.77, "curve": [2.733, 18.77, 2.817, 8.29]}, {"time": 2.8667, "value": 6.51, "curve": [2.988, 2.17, 3.058, 0]}, {"time": 3.1667}]}, "flare1": {"rotate": [{"time": 1.1, "value": 8.2}], "translate": [{"time": 1.1, "x": -19.97, "y": 149.68}, {"time": 1.2, "x": 3.85, "y": 152.43}, {"time": 1.2333, "x": -15.42, "y": 152.29}], "scale": [{"time": 1.1, "x": 0.805, "y": 0.805, "curve": [1.119, 0.763, 1.16, 1.162, 1.117, 0.805, 1.15, 0.605]}, {"time": 1.1667, "x": 1.279, "y": 0.605, "curve": [1.177, 1.47, 1.192, 2.151, 1.175, 0.605, 1.192, 0.911]}, {"time": 1.2, "x": 2.151, "y": 0.911, "curve": [1.208, 2.151, 1.231, 1.668, 1.208, 0.911, 1.227, 0.844]}, {"time": 1.2333, "x": 1.608, "y": 0.805, "curve": [1.249, 1.205, 1.283, 0.547, 1.254, 0.685, 1.283, 0.416]}, {"time": 1.3, "x": 0.547, "y": 0.416}], "shear": [{"time": 1.1, "y": 4.63}, {"time": 1.2333, "x": -5.74, "y": 4.63}]}, "flare2": {"rotate": [{"time": 1.1, "value": 12.29}], "translate": [{"time": 1.1, "x": -8.63, "y": 132.96}, {"time": 1.2, "x": 4.35, "y": 132.93}], "scale": [{"time": 1.1, "x": 0.864, "y": 0.864}, {"time": 1.1667, "x": 0.945, "y": 0.945}, {"time": 1.2, "x": 1.511, "y": 1.081}], "shear": [{"time": 1.1, "y": 24.03}]}, "flare3": {"rotate": [{"time": 1.1667, "value": 2.88}], "translate": [{"time": 1.1667, "x": 3.24, "y": 114.81}], "scale": [{"time": 1.1667, "x": 0.668, "y": 0.668}], "shear": [{"time": 1.1667, "y": 38.59}]}, "flare4": {"rotate": [{"time": 1.1667, "value": -8.64}], "translate": [{"time": 1.1667, "x": -3.82, "y": 194.06}, {"time": 1.2667, "x": -1.82, "y": 198.47, "curve": "stepped"}, {"time": 1.3, "x": -1.94, "y": 187.81}], "scale": [{"time": 1.1667, "x": 0.545, "y": 0.545}, {"time": 1.2667, "x": 0.757, "y": 0.757}], "shear": [{"time": 1.1667, "x": 7.42, "y": -22.04}]}, "flare5": {"translate": [{"time": 1.2, "x": -11.17, "y": 176.42}, {"time": 1.2333, "x": -8.56, "y": 179.04, "curve": "stepped"}, {"time": 1.3, "x": -14.57, "y": 168.69}], "scale": [{"time": 1.2333, "x": 1.146}, {"time": 1.3, "x": 0.703, "y": 0.61}], "shear": [{"time": 1.2, "x": 6.9}]}, "flare6": {"rotate": [{"time": 1.2333, "value": -5.36}, {"time": 1.2667, "value": -0.54}], "translate": [{"time": 1.2333, "x": 14.52, "y": 204.67}, {"time": 1.2667, "x": 19.16, "y": 212.9, "curve": "stepped"}, {"time": 1.3, "x": 9.23, "y": 202.85}], "scale": [{"time": 1.2333, "x": 0.777, "y": 0.49}, {"time": 1.2667, "x": 0.777, "y": 0.657}, {"time": 1.3, "x": 0.475, "y": 0.401}]}, "flare7": {"rotate": [{"time": 1.1, "value": 5.98}, {"time": 1.1333, "value": 32.82}], "translate": [{"time": 1.1, "x": -6.34, "y": 112.98}, {"time": 1.1333, "x": 2.66, "y": 111.6}], "scale": [{"time": 1.1, "x": 0.588, "y": 0.588}], "shear": [{"time": 1.1333, "x": -19.93}]}, "flare8": {"rotate": [{"time": 1.2333, "value": -6.85}], "translate": [{"time": 1.1667, "x": 66.67, "y": 125.52, "curve": "stepped"}, {"time": 1.2, "x": 58.24, "y": 113.53, "curve": "stepped"}, {"time": 1.2333, "x": 40.15, "y": 114.69}], "scale": [{"time": 1.1667, "x": 1.313, "y": 1.203}, {"time": 1.2333, "x": 1.038, "y": 0.95}], "shear": [{"time": 1.2, "y": -13.01}]}, "flare9": {"rotate": [{"time": 1.1667, "value": 2.9}], "translate": [{"time": 1.1667, "x": 28.45, "y": 151.35, "curve": "stepped"}, {"time": 1.2, "x": 48.8, "y": 191.09, "curve": "stepped"}, {"time": 1.2333, "x": 52, "y": 182.52, "curve": "stepped"}, {"time": 1.2667, "x": 77.01, "y": 195.96}], "scale": [{"time": 1.1667, "x": 0.871, "y": 1.073}, {"time": 1.2, "x": 0.927, "y": 0.944}, {"time": 1.2333, "x": 1.165, "y": 1.336}], "shear": [{"time": 1.1667, "x": 7.95, "y": 25.48}]}, "flare10": {"rotate": [{"time": 1.1667, "value": 2.18}], "translate": [{"time": 1.1667, "x": 55.64, "y": 137.64, "curve": "stepped"}, {"time": 1.2, "x": 90.49, "y": 151.07, "curve": "stepped"}, {"time": 1.2333, "x": 114.06, "y": 153.05, "curve": "stepped"}, {"time": 1.2667, "x": 90.44, "y": 164.61}], "scale": [{"time": 1.1667, "x": 2.657, "y": 0.891}, {"time": 1.2, "x": 3.314, "y": 1.425}, {"time": 1.2333, "x": 2.871, "y": 0.924}, {"time": 1.2667, "x": 2.317, "y": 0.775}], "shear": [{"time": 1.1667, "x": -1.35}]}, "torso2": {"rotate": [{"time": 1, "curve": [1.117, 0, 1.255, 24.94]}, {"time": 1.4, "value": 24.94, "curve": [1.477, 24.94, 1.59, -17.62]}, {"time": 1.6333, "value": -19.48, "curve": [1.717, -23.1, 1.784, -26.12]}, {"time": 1.9333, "value": -26.14, "curve": [2.067, -26.15, 2.158, 4.3]}, {"time": 2.3, "value": 4.22, "curve": [2.45, 4.13, 2.579, -1.76]}, {"time": 2.7333, "value": -1.8, "curve": [2.816, -1.82, 2.857, -2.94]}, {"time": 2.9333, "value": -2.99, "curve": [3.056, -3.08, 3.09, 0]}, {"time": 3.1667}]}, "torso3": {"rotate": [{"time": 1.3, "curve": [1.352, 0, 1.408, 6.47]}, {"time": 1.4667, "value": 6.43, "curve": [1.55, 6.39, 1.723, -5.05]}, {"time": 1.7333, "value": -5.53, "curve": [1.782, -7.72, 1.843, -16.94]}, {"time": 1.9667, "value": -16.86, "curve": [2.111, -16.78, 2.259, -3.97]}, {"time": 2.4, "value": -2.43, "curve": [2.525, -1.12, 2.639, -0.5]}, {"time": 2.7333, "value": -0.49, "curve": [2.931, -0.47, 2.999, -2.15]}, {"time": 3.1667, "value": -2.15}]}, "head-control": {"translate": [{"time": 1.2333, "curve": [1.25, 0, 1.474, 6.89, 1.25, 0, 1.496, 0.98]}, {"time": 1.6667, "x": 11.99, "y": -6.42, "curve": [1.743, 14.01, 1.86, 14.33, 1.785, -11.55, 1.86, -27.1]}, {"time": 1.9667, "x": 13.91, "y": -26.88, "curve": [2.074, 13.49, 2.244, 8.13, 2.074, -26.65, 2.215, -21.78]}, {"time": 2.3, "x": 6.07, "y": -16.64, "curve": [2.416, 1.84, 2.497, -1.41, 2.417, -9.57, 2.526, -1.72]}, {"time": 2.5667, "x": -3.78, "y": -1.71, "curve": [2.661, -6.98, 2.76, -8.76, 2.692, -1.68, 2.821, -15.75]}, {"time": 2.9, "x": -8.32, "y": -16.7, "curve": [2.962, -8.12, 3.082, -0.04, 2.958, -17.39, 3.089, 0]}, {"time": 3.1667}]}, "front-shoulder": {"translate": [{"time": 1.3333, "curve": [1.488, 0, 1.717, 0.21, 1.488, 0, 1.688, -30.29]}, {"time": 1.9, "x": 0.83, "y": -30.29, "curve": [2.078, 1.43, 2.274, 2.88, 2.071, -30.29, 2.289, 4.48]}, {"time": 2.4333, "x": 2.89, "y": 4.59, "curve": [2.604, 2.89, 2.677, -0.68, 2.57, 4.7, 2.694, -2.43]}, {"time": 2.7667, "x": -0.67, "y": -2.47, "curve": [2.866, -0.67, 2.986, -0.07, 2.882, -2.47, 3.036, -0.06]}, {"time": 3.1667}]}}, "ik": {"rear-leg-ik": [{"time": 3.1667, "softness": 10, "bendPositive": false}]}}, "run": {"slots": {"mouth": {"attachment": [{"name": "mouth-grind"}]}}, "bones": {"front-thigh": {"translate": [{"x": -5.14, "y": 11.13, "curve": [0.033, -7.77, 0.112, -9.03, 0.034, 11.13, 0.108, 9.74]}, {"time": 0.1667, "x": -9.03, "y": 7.99, "curve": [0.23, -9.05, 0.314, -1.34, 0.236, 5.93, 0.28, 3.22]}, {"time": 0.3333, "x": 0.41, "y": 3.19, "curve": [0.352, 2.09, 0.449, 11.16, 0.384, 3.16, 0.449, 4.98]}, {"time": 0.5, "x": 11.17, "y": 6.76, "curve": [0.571, 10.79, 0.621, -1.83, 0.542, 8.21, 0.625, 11.13]}, {"time": 0.6667, "x": -5.14, "y": 11.13}]}, "torso": {"rotate": [{"value": -37.66, "curve": [0.034, -37.14, 0.107, -36.21]}, {"time": 0.1333, "value": -36.21, "curve": [0.158, -36.21, 0.209, -38.8]}, {"time": 0.2333, "value": -38.79, "curve": [0.259, -38.78, 0.313, -38.03]}, {"time": 0.3333, "value": -37.66, "curve": [0.357, -37.21, 0.4, -36.21]}, {"time": 0.4333, "value": -36.21, "curve": [0.458, -36.21, 0.539, -38.8]}, {"time": 0.5667, "value": -38.8, "curve": [0.592, -38.8, 0.645, -38]}, {"time": 0.6667, "value": -37.66}]}, "rear-thigh": {"translate": [{"x": -16.41, "y": 1.55, "curve": [0.013, -15.67, 0.183, -8.55, 0.03, 2.39, 0.183, 6.17]}, {"time": 0.2333, "x": -8.55, "y": 6.17, "curve": [0.308, -8.55, 0.492, -19.75, 0.308, 6.17, 0.492, 0.61]}, {"time": 0.5667, "x": -19.75, "y": 0.61, "curve": [0.592, -19.75, 0.641, -18.06, 0.592, 0.61, 0.632, 0.78]}, {"time": 0.6667, "x": -16.41, "y": 1.55}]}, "front-upper-arm": {"rotate": [{"value": -39.03, "curve": [0.051, -0.1, 0.145, 88.36]}, {"time": 0.2333, "value": 88.36, "curve": [0.28, 88.76, 0.324, 59.52]}, {"time": 0.3333, "value": 51.13, "curve": [0.358, 30.2, 0.445, -74.91]}, {"time": 0.5667, "value": -75.82, "curve": [0.599, -76.06, 0.642, -55.72]}, {"time": 0.6667, "value": -39.03}]}, "front-bracer": {"rotate": [{"value": 20.54, "curve": [0.052, 11.42, 0.089, 0.13]}, {"time": 0.1333, "value": 0.15, "curve": [0.186, 0.17, 0.221, 26.29]}, {"time": 0.2333, "value": 32.37, "curve": [0.247, 39.19, 0.286, 61.45]}, {"time": 0.3333, "value": 61.58, "curve": [0.371, 61.69, 0.42, 55.79]}, {"time": 0.4667, "value": 49.68}, {"time": 0.6667, "value": 20.54}]}, "front-fist": {"rotate": [{"value": -36.16, "curve": [0.014, -38.8, 0.036, -43.27]}, {"time": 0.0667, "value": -43.37, "curve": [0.102, -43.49, 0.182, -28.46]}, {"time": 0.2, "value": -23.04, "curve": [0.23, -13.87, 0.264, 3.86]}, {"time": 0.3333, "value": 3.7, "curve": [0.38, 3.64, 0.535, -16.22]}, {"time": 0.5667, "value": -21.29}, {"time": 0.6667, "value": -36.16}]}, "rear-upper-arm": {"rotate": [{"value": 40.5, "curve": [0.028, 23.74, 0.128, -79.86]}, {"time": 0.2333, "value": -79.87, "curve": [0.38, -79.88, 0.403, 63.25]}, {"time": 0.5667, "value": 64.13, "curve": [0.607, 64.35, 0.644, 53.1]}, {"time": 0.6667, "value": 40.5}], "translate": [{"x": -3.79, "y": -0.77, "curve": [0.044, -4.58, 0.169, -5.48, 0.044, 0.93, 0.169, 2.85]}, {"time": 0.2333, "x": -5.48, "y": 2.85, "curve": [0.346, -5.48, 0.475, -2.68, 0.346, 2.85, 0.475, -3.13]}, {"time": 0.5667, "x": -2.68, "y": -3.13, "curve": [0.611, -2.68, 0.642, -3.34, 0.611, -3.13, 0.642, -1.73]}, {"time": 0.6667, "x": -3.79, "y": -0.77}]}, "rear-bracer": {"rotate": [{"value": 28.28}, {"time": 0.2333, "value": -11.12, "curve": [0.252, -14.12, 0.297, -19.37]}, {"time": 0.3333, "value": -19.38, "curve": [0.435, -19.41, 0.522, 38.96]}, {"time": 0.5667, "value": 38.87, "curve": [0.619, 38.76, 0.644, 32.01]}, {"time": 0.6667, "value": 28.28}]}, "neck": {"rotate": [{"value": 11.88, "curve": [0.024, 11.4, 0.075, 9.74]}, {"time": 0.1, "value": 9.74, "curve": [0.125, 9.74, 0.208, 13.36]}, {"time": 0.2333, "value": 13.36, "curve": [0.258, 13.36, 0.321, 12.2]}, {"time": 0.3333, "value": 11.88, "curve": [0.365, 11.06, 0.408, 9.72]}, {"time": 0.4333, "value": 9.72, "curve": [0.458, 9.72, 0.542, 13.36]}, {"time": 0.5667, "value": 13.36, "curve": [0.592, 13.36, 0.636, 12.48]}, {"time": 0.6667, "value": 11.88}]}, "head": {"rotate": [{"value": 13.14, "curve": [0.02, 11.99, 0.039, 8.94]}, {"time": 0.0667, "value": 8.93, "curve": [0.122, 8.9, 0.232, 15.8]}, {"time": 0.2667, "value": 15.81, "curve": [0.325, 15.82, 0.357, 8.95]}, {"time": 0.4, "value": 8.93, "curve": [0.444, 8.91, 0.568, 15.8]}, {"time": 0.6, "value": 15.77, "curve": [0.632, 15.74, 0.649, 14.05]}, {"time": 0.6667, "value": 13.14}], "scale": [{"curve": [0.014, 0.996, 0.068, 0.991, 0.027, 1.005, 0.083, 1.012]}, {"time": 0.1, "x": 0.991, "y": 1.012, "curve": [0.128, 0.991, 0.205, 1.018, 0.128, 1.012, 0.197, 0.988]}, {"time": 0.2333, "x": 1.018, "y": 0.988, "curve": [0.272, 1.018, 0.305, 1.008, 0.262, 0.988, 0.311, 0.995]}, {"time": 0.3333, "curve": [0.351, 0.995, 0.417, 0.987, 0.359, 1.006, 0.417, 1.013]}, {"time": 0.4333, "x": 0.987, "y": 1.013, "curve": [0.467, 0.987, 0.533, 1.02, 0.467, 1.013, 0.533, 0.989]}, {"time": 0.5667, "x": 1.02, "y": 0.989, "curve": [0.592, 1.02, 0.652, 1.004, 0.592, 0.989, 0.644, 0.996]}, {"time": 0.6667}]}, "gun": {"rotate": [{"value": 12.36, "curve": [0.022, 16.28, 0.087, 20.25]}, {"time": 0.1333, "value": 20.19, "curve": [0.168, 20.32, 0.254, -8.82]}, {"time": 0.2667, "value": -11.88, "curve": [0.291, -17.91, 0.344, -24.11]}, {"time": 0.4, "value": -23.88, "curve": [0.448, -23.69, 0.533, -15.47]}, {"time": 0.5667, "value": -8.69}, {"time": 0.6667, "value": 12.36}]}, "hip": {"rotate": [{"value": -8.24}], "translate": [{"x": -3.6, "y": -34.1, "curve": [0.042, -3.84, 0.118, 7.62, 0.042, -33.74, 0.112, 20.55]}, {"time": 0.1667, "x": 7.61, "y": 20.36, "curve": [0.194, 7.6, 0.21, 5.06, 0.204, 20.65, 0.217, -8.69]}, {"time": 0.2333, "x": 1.68, "y": -18.48, "curve": [0.279, -4.99, 0.297, -5.64, 0.254, -31.08, 0.292, -34.55]}, {"time": 0.3333, "x": -5.76, "y": -35, "curve": [0.379, -5.9, 0.451, 6.8, 0.384, -35.56, 0.428, 17.6]}, {"time": 0.5, "x": 6.61, "y": 17.01, "curve": [0.536, 6.47, 0.545, 3.56, 0.533, 16.75, 0.548, -8.71]}, {"time": 0.5667, "x": 0.35, "y": -18.81, "curve": [0.597, -4.07, 0.642, -3.45, 0.584, -28.58, 0.642, -34.32]}, {"time": 0.6667, "x": -3.6, "y": -34.1}]}, "front-foot-target": {"rotate": [{"value": -62.54, "curve": [0.015, -74.19, 0.056, -103.19]}, {"time": 0.0667, "value": -111.08, "curve": [0.092, -129.44, 0.189, -146.55]}, {"time": 0.2333, "value": -146.32, "curve": [0.285, -146.06, 0.32, -125.1]}, {"time": 0.3333, "value": -117.24}, {"time": 0.5, "value": -35.07, "curve": [0.522, -28.64, 0.546, -24.84]}, {"time": 0.5667, "value": -24.9, "curve": [0.595, -25, 0.623, -40.82]}, {"time": 0.6667, "value": -62.54}], "translate": [{"x": 16.34, "y": 0.18}, {"time": 0.0667, "x": -101.43, "y": 8.04, "curve": [0.085, -131.35, 0.129, -207.69, 0.08, 14.9, 0.124, 113.28]}, {"time": 0.1667, "x": -207.92, "y": 145.81, "curve": [0.196, -208.13, 0.21, -202.91, 0.186, 160.26, 0.206, 163.48]}, {"time": 0.2333, "x": -189.94, "y": 163.85, "curve": [0.27, -169.94, 0.31, -126.19, 0.269, 164.35, 0.316, 85.97]}, {"time": 0.3333, "x": -90.56, "y": 78.57, "curve": [0.355, -57.99, 0.376, -29.14, 0.35, 71.55, 0.376, 66.4]}, {"time": 0.4, "x": 2.87, "y": 66.38, "curve": [0.412, 19.24, 0.469, 90.73, 0.429, 66.37, 0.469, 70.66]}, {"time": 0.5, "x": 117.18, "y": 70.46, "curve": [0.522, 136.24, 0.542, 151.33, 0.539, 70.2, 0.555, 38.25]}, {"time": 0.5667, "x": 151.49, "y": 25.29, "curve": [0.578, 146.76, 0.586, 133.13, 0.572, 19.7, 0.582, 12.23]}, {"time": 0.6, "x": 115.02, "y": 0.1}, {"time": 0.6667, "x": 16.34, "y": 0.18}]}, "front-leg-target": {"translate": [{"x": -13.95, "y": -30.34}]}, "rear-foot-target": {"rotate": [{"value": 18.55}, {"time": 0.2333, "value": 167.84, "curve": [0.246, 153.66, 0.256, 129.74]}, {"time": 0.2667, "value": 124.32, "curve": [0.296, 124.43, 0.313, 129.93]}, {"time": 0.3667, "value": 129.87, "curve": [0.421, 128.32, 0.519, 0.98]}, {"time": 0.5667, "curve": [0.6, 0.27, 0.642, 4.73]}, {"time": 0.6667, "value": 18.55}], "translate": [{"x": -176.39, "y": 134.12, "curve": [0.018, -142.26, 0.054, -94.41, 0.01, 120.96, 0.044, 84.08]}, {"time": 0.0667, "x": -73.56, "y": 76.68, "curve": [0.086, -42.82, 0.194, 101.2, 0.098, 66.73, 0.198, 60.88]}, {"time": 0.2333, "x": 98.32, "y": 32.17}, {"time": 0.2667, "x": 49.13, "y": -0.63}, {"time": 0.4, "x": -147.9, "y": 0.32, "curve": [0.414, -168.78, 0.478, -284.76, 0.43, 30.09, 0.478, 129.14]}, {"time": 0.5, "x": -283.37, "y": 167.12, "curve": [0.526, -285.66, 0.548, -280.54, 0.516, 194.84, 0.55, 216.53]}, {"time": 0.5667, "x": -266.98, "y": 216.12, "curve": [0.581, -256.27, 0.643, -206.54, 0.61, 214.82, 0.65, 145.33]}, {"time": 0.6667, "x": -176.39, "y": 134.12}]}, "rear-leg-target": {"translate": [{"x": 85, "y": -33.59}]}, "back-foot-tip": {"rotate": [{"value": -147.04, "curve": [0.033, -113.4, 0.161, 44.34]}, {"time": 0.2333, "value": 43.48, "curve": [0.24, 43.41, 0.282, 35.72]}, {"time": 0.3, "value": 0.29, "curve": [0.347, 0.28, 0.396, 4.27]}, {"time": 0.4, "curve": [0.424, -23.8, 0.525, -181.39]}, {"time": 0.5667, "value": -181.39, "curve": [0.592, -181.39, 0.642, -169.09]}, {"time": 0.6667, "value": -147.04}]}, "front-foot-tip": {"rotate": [{"value": -0.25, "curve": [0.008, -0.25, 0.056, 1.73]}, {"time": 0.0667, "value": -7.68, "curve": [0.075, -43.13, 0.15, -130.44]}, {"time": 0.2, "value": -130.08, "curve": [0.239, -129.79, 0.272, -126.8]}, {"time": 0.3, "value": -116.24, "curve": [0.333, -103.91, 0.348, -86.1]}, {"time": 0.3667, "value": -71.08, "curve": [0.386, -55.25, 0.415, -32.44]}, {"time": 0.4333, "value": -21.63, "curve": [0.47, -0.01, 0.542, 33.42]}, {"time": 0.5667, "value": 33.2, "curve": [0.622, 32.7, 0.569, 0.64]}, {"time": 0.6667, "value": -0.25}]}, "hair1": {"rotate": [{"value": -6.81, "curve": [0.087, -6.81, 0.143, -5.75]}, {"time": 0.1667, "value": -4.3, "curve": [0.183, -3.28, 0.209, 2.79]}, {"time": 0.2333, "value": 2.78, "curve": [0.262, 2.77, 0.305, -6.63]}, {"time": 0.3333, "value": -6.64, "curve": [0.419, -6.68, 0.49, -4.84]}, {"time": 0.5, "value": -4.38, "curve": [0.518, -3.56, 0.574, 2.32]}, {"time": 0.6, "value": 2.33, "curve": [0.643, 2.35, 0.633, -6.81]}, {"time": 0.6667, "value": -6.81}]}, "hair2": {"rotate": [{"value": -6.81, "curve": [0.014, -3.17, 0.109, 43.93]}, {"time": 0.1333, "value": 43.95, "curve": [0.177, 43.97, 0.192, -13.76]}, {"time": 0.2667, "value": -13.83, "curve": [0.302, -13.72, 0.322, -8.86]}, {"time": 0.3333, "value": -6.6, "curve": [0.349, -3.5, 0.436, 41.1]}, {"time": 0.4667, "value": 41.05, "curve": [0.51, 40.99, 0.549, -14.06]}, {"time": 0.6, "value": -14.18, "curve": [0.63, -14.26, 0.656, -9.04]}, {"time": 0.6667, "value": -6.81}]}, "hair3": {"rotate": [{"value": -6.81, "curve": [0.079, -6.83, 0.108, 0.3]}, {"time": 0.1333, "value": 1.96, "curve": [0.177, 4.89, 0.208, 6.28]}, {"time": 0.2333, "value": 6.29, "curve": [0.313, 6.31, 0.383, 3.49]}, {"time": 0.4, "value": 2.58, "curve": [0.442, 0.28, 0.523, -6.81]}, {"time": 0.6, "value": -6.81}]}, "hair4": {"rotate": [{"value": -6.81, "curve": [0.011, -4.06, 0.108, 24.92]}, {"time": 0.1333, "value": 24.92, "curve": [0.158, 24.92, 0.208, -10.62]}, {"time": 0.2333, "value": -10.62, "curve": [0.254, -10.62, 0.312, -9.73]}, {"time": 0.3333, "value": -6.4, "curve": [0.356, -2.95, 0.438, 24.93]}, {"time": 0.4667, "value": 24.93, "curve": [0.492, 24.93, 0.575, -9.78]}, {"time": 0.6, "value": -9.78, "curve": [0.617, -9.78, 0.655, -8.63]}, {"time": 0.6667, "value": -6.81}]}, "torso2": {"rotate": [{"value": 3.5, "curve": [0.07, 3.51, 0.075, 8.69]}, {"time": 0.1, "value": 8.69, "curve": [0.139, 8.69, 0.214, 6.9]}, {"time": 0.2333, "value": 6.33, "curve": [0.266, 5.34, 0.285, 3.48]}, {"time": 0.3333, "value": 3.48, "curve": [0.398, 3.48, 0.408, 8.68]}, {"time": 0.4333, "value": 8.68, "curve": [0.458, 8.68, 0.551, 6.8]}, {"time": 0.5667, "value": 6.26, "curve": [0.598, 5.17, 0.642, 3.49]}, {"time": 0.6667, "value": 3.5}]}, "torso3": {"rotate": [{"value": 4.52, "curve": [0.067, 4.54, 0.075, -7.27]}, {"time": 0.1, "value": -7.27, "curve": [0.125, -7.27, 0.227, 0.84]}, {"time": 0.2333, "value": 1.24, "curve": [0.254, 2.5, 0.301, 4.51]}, {"time": 0.3333, "value": 4.52, "curve": [0.386, 4.54, 0.408, -7.35]}, {"time": 0.4333, "value": -7.35, "curve": [0.458, -7.35, 0.549, -0.14]}, {"time": 0.5667, "value": 0.95, "curve": [0.586, 2.18, 0.632, 4.54]}, {"time": 0.6667, "value": 4.52}]}, "aim-constraint-target": {"rotate": [{"value": 30.57}]}, "rear-foot": {"rotate": [{"value": -6.5}]}, "front-foot": {"rotate": [{"value": 4.5}]}, "head-control": {"translate": [{"y": -9.94, "curve": [0.058, 0, 0.175, -15.32, 0.044, -4.19, 0.175, 5]}, {"time": 0.2333, "x": -15.32, "y": 5, "curve": [0.317, -15.32, 0.429, -9.74, 0.317, 5, 0.382, -31.71]}, {"time": 0.4667, "x": -7.81, "y": -31.59, "curve": [0.507, -5.76, 0.617, 0, 0.549, -31.47, 0.628, -13.33]}, {"time": 0.6667, "y": -9.94}]}, "front-shoulder": {"translate": [{"x": -0.74, "y": 11.22, "curve": [0.061, -0.74, 0.144, 1.17, 0.061, 11.22, 0.143, -17.93]}, {"time": 0.2333, "x": 1.19, "y": -17.9, "curve": [0.54, 1.25, 0.558, -0.74, 0.545, -17.8, 0.558, 11.22]}, {"time": 0.6667, "x": -0.74, "y": 11.22}]}, "back-shoulder": {"translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, 8.93]}, {"time": 0.3333, "y": 8.93, "curve": [0.417, 0, 0.583, 0, 0.417, 8.93, 0.583, 0]}, {"time": 0.6667}]}}, "ik": {"front-leg-ik": [{"softness": 10, "bendPositive": false}, {"time": 0.5667, "softness": 14.8, "bendPositive": false}, {"time": 0.6, "softness": 48.2, "bendPositive": false}, {"time": 0.6667, "softness": 10, "bendPositive": false}], "rear-leg-ik": [{"bendPositive": false}, {"time": 0.1667, "softness": 22.5, "bendPositive": false}, {"time": 0.3, "softness": 61.4, "bendPositive": false}, {"time": 0.6667, "bendPositive": false}]}, "events": [{"time": 0.2333, "name": "footstep"}, {"time": 0.5667, "name": "footstep"}]}, "run-to-idle": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-foot-target": {"translate": [{"x": -16.5, "y": 3.41, "curve": [0.033, -16.5, 0.1, -69.06, 0.033, 3.41, 0.1, 0]}, {"time": 0.1333, "x": -69.06}]}, "hip": {"translate": [{"x": -28.78, "y": -72.96, "curve": [0.036, -28.63, 0.2, -10.85, 0.135, -62.35, 0.2, -23.15]}, {"time": 0.2667, "x": -11.97, "y": -23.15}]}, "rear-foot-target": {"translate": [{"x": 33.15, "y": 31.61, "curve": [0.017, 33.15, 0.05, 24.41, 0.017, 31.61, 0.041, 20.73]}, {"time": 0.0667, "x": 24.41, "y": 0.19, "curve": [0.117, 24.41, 0.217, 48.87, 0.117, 0.19, 0.217, 0]}, {"time": 0.2667, "x": 48.87}]}, "front-upper-arm": {"rotate": [{"value": -80.61, "curve": [0.067, -80.61, 0.2, -60.87]}, {"time": 0.2667, "value": -60.87}]}, "front-bracer": {"rotate": [{"value": 8.79, "curve": [0.041, 8.79, 0.115, 6.3]}, {"time": 0.1667, "value": 6.41, "curve": [0.201, 6.48, 0.241, 42.46]}, {"time": 0.2667, "value": 42.46}]}, "rear-upper-arm": {"rotate": [{"value": 55.3, "curve": [0.067, 55.3, 0.2, 39.2]}, {"time": 0.2667, "value": 39.2}]}, "head": {"rotate": [{"curve": [0.05, 0, 0.083, 2.67]}, {"time": 0.1333, "value": 2.67, "curve": [0.15, 2.67, 0.25, -6.75]}, {"time": 0.2667, "value": -6.75}]}, "front-fist": {"rotate": [{"value": 38.26, "curve": [0.041, 38.26, 0.127, -2.19]}, {"time": 0.1667, "value": -3, "curve": [0.209, -3.84, 0.241, 0]}, {"time": 0.2667}], "scale": [{"x": 0.844, "curve": [0.067, 0.844, 0.2, 1, 0.067, 1, 0.2, 1]}, {"time": 0.2667}]}, "rear-bracer": {"rotate": [{"value": 57.24, "curve": [0.067, 57.24, 0.2, 0]}, {"time": 0.2667}]}, "gun": {"rotate": [{"value": 2.28, "curve": [0.041, 2.28, 0.105, 15.34]}, {"time": 0.1667, "value": 15.32, "curve": [0.205, 15.31, 0.241, 0]}, {"time": 0.2667}]}, "torso": {"rotate": [{"value": -12.98, "curve": [0.033, -12.98, 0.103, -14.81]}, {"time": 0.1333, "value": -16.63, "curve": [0.168, -18.69, 0.233, -22.88]}, {"time": 0.2667, "value": -22.88}], "scale": [{"x": 0.963, "y": 1.074, "curve": [0.067, 0.963, 0.132, 1, 0.067, 1.074, 0.132, 1]}, {"time": 0.2667}]}, "neck": {"rotate": [{}, {"time": 0.2667, "value": 3.78}]}, "hair3": {"rotate": [{"curve": [0.033, 0, 0.1, 0.88]}, {"time": 0.1333, "value": 0.88, "curve": [0.167, 0.88, 0.233, 0]}, {"time": 0.2667}]}, "hair4": {"rotate": [{"curve": [0.033, 0, 0.1, 15.97]}, {"time": 0.1333, "value": 15.97, "curve": [0.167, 15.97, 0.233, 0]}, {"time": 0.2667}]}, "hair1": {"rotate": [{"curve": [0.033, 0, 0.1, 10.76]}, {"time": 0.1333, "value": 10.76, "curve": [0.167, 10.76, 0.233, 0]}, {"time": 0.2667}]}, "hair2": {"rotate": [{"curve": [0.014, -2.28, 0.042, -7.84]}, {"time": 0.0667, "value": -7.82, "curve": [0.108, -7.79, 0.166, 6.57]}, {"time": 0.2, "value": 6.67, "curve": [0.222, 6.73, 0.255, 1.98]}, {"time": 0.2667}]}, "torso2": {"rotate": [{"curve": [0.041, 0, 0.107, 3.03]}, {"time": 0.1667, "value": 3.03, "curve": [0.205, 3.03, 0.241, 0]}, {"time": 0.2667}]}, "torso3": {"rotate": [{"curve": [0.049, 0, 0.166, 0.66]}, {"time": 0.2, "value": 0.66, "curve": [0.232, 0.65, 0.249, -2.15]}, {"time": 0.2667, "value": -2.15}]}, "head-control": {"translate": [{"x": -10.12, "y": 8.71}, {"time": 0.2667}]}, "front-shoulder": {"translate": [{"x": 4.91, "y": 11.54}, {"time": 0.2667}]}}}, "shoot": {"slots": {"muzzle": {"rgba": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff62"}], "attachment": [{"time": 0.0333, "name": "muzzle01"}, {"time": 0.0667, "name": "muzzle02"}, {"time": 0.1, "name": "muzzle03"}, {"time": 0.1333, "name": "muzzle04"}, {"time": 0.1667, "name": "muzzle05"}, {"time": 0.2}]}, "muzzle-glow": {"rgba": [{"color": "ff0c0c00"}, {"time": 0.0333, "color": "ffc9adff", "curve": [0.255, 1, 0.273, 1, 0.255, 0.76, 0.273, 0.4, 0.255, 0.65, 0.273, 0.22, 0.255, 1, 0.273, 1]}, {"time": 0.3, "color": "ff400cff"}, {"time": 0.6333, "color": "ff0c0c00"}], "attachment": [{"name": "muzzle-glow"}]}, "muzzle-ring": {"rgba": [{"time": 0.0333, "color": "d8baffff", "curve": [0.202, 0.85, 0.214, 0.84, 0.202, 0.73, 0.214, 0.73, 0.202, 1, 0.214, 1, 0.202, 1, 0.214, 0.21]}, {"time": 0.2333, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2333}]}, "muzzle-ring2": {"rgba": [{"time": 0.0333, "color": "d8baffff", "curve": [0.174, 0.85, 0.184, 0.84, 0.174, 0.73, 0.184, 0.73, 0.174, 1, 0.184, 1, 0.174, 1, 0.184, 0.21]}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2}]}, "muzzle-ring3": {"rgba": [{"time": 0.0333, "color": "d8baffff", "curve": [0.174, 0.85, 0.184, 0.84, 0.174, 0.73, 0.184, 0.73, 0.174, 1, 0.184, 1, 0.174, 1, 0.184, 0.21]}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2}]}, "muzzle-ring4": {"rgba": [{"time": 0.0333, "color": "d8baffff", "curve": [0.174, 0.85, 0.184, 0.84, 0.174, 0.73, 0.184, 0.73, 0.174, 1, 0.184, 1, 0.174, 1, 0.184, 0.21]}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2}]}}, "bones": {"gun": {"rotate": [{"time": 0.0667, "curve": [0.094, 25.89, 0.112, 45.27]}, {"time": 0.1333, "value": 45.35, "curve": [0.192, 45.28, 0.18, -0.09]}, {"time": 0.6333}]}, "muzzle": {"translate": [{"x": -11.02, "y": 25.16}]}, "rear-upper-arm": {"translate": [{"time": 0.0333, "curve": [0.045, 0.91, 0.083, 3.46, 0.044, 0.86, 0.083, 3.32]}, {"time": 0.1, "x": 3.46, "y": 3.32, "curve": [0.133, 3.46, 0.176, -0.1, 0.133, 3.32, 0.169, 0]}, {"time": 0.2333}]}, "rear-bracer": {"translate": [{"time": 0.0333, "curve": [0.075, -3.78, 0.083, -4.36, 0.08, -2.7, 0.083, -2.88]}, {"time": 0.1, "x": -4.36, "y": -2.88, "curve": [0.133, -4.36, 0.168, 0.18, 0.133, -2.88, 0.167, 0]}, {"time": 0.2333}]}, "gun-tip": {"translate": [{}, {"time": 0.3, "x": 3.15, "y": 0.39}], "scale": [{"x": 0.366, "y": 0.366}, {"time": 0.0333, "x": 1.453, "y": 1.453}, {"time": 0.3, "x": 0.366, "y": 0.366}]}, "muzzle-ring": {"translate": [{"time": 0.0333}, {"time": 0.2333, "x": 64.47}], "scale": [{"time": 0.0333}, {"time": 0.2333, "x": 5.951, "y": 5.951}]}, "muzzle-ring2": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 172.57}], "scale": [{"time": 0.0333}, {"time": 0.2, "x": 4, "y": 4}]}, "muzzle-ring3": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 277.17}], "scale": [{"time": 0.0333}, {"time": 0.2, "x": 2, "y": 2}]}, "muzzle-ring4": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 392.06}]}}}, "walk": {"bones": {"rear-foot-target": {"rotate": [{"value": -32.82, "curve": [0.035, -42.69, 0.057, -70.49]}, {"time": 0.1, "value": -70.59, "curve": [0.236, -70.78, 0.335, -9.87]}, {"time": 0.3667, "value": -1.56, "curve": [0.393, 5.5, 0.477, 13.96]}, {"time": 0.5, "value": 13.96, "curve": [0.519, 13.96, 0.508, 0.13]}, {"time": 0.5667, "value": -0.28}, {"time": 0.7333, "value": -0.28, "curve": [0.827, -0.06, 0.958, -21.07]}, {"time": 1, "value": -32.82}], "translate": [{"x": -167.32, "y": 0.58, "curve": [0.022, -180.55, 0.075, -235.51, 0.045, 0.58, 0.075, 30.12]}, {"time": 0.1, "x": -235.51, "y": 39.92, "curve": [0.142, -235.51, 0.208, -201.73, 0.138, 54.94, 0.18, 60.78]}, {"time": 0.2333, "x": -176.33, "y": 61.48, "curve": [0.272, -136.61, 0.321, -45.18, 0.275, 62.02, 0.321, 56.6]}, {"time": 0.3667, "x": 8.44, "y": 49.67, "curve": [0.403, 51.03, 0.486, 66.86, 0.401, 44.37, 0.48, 23.11]}, {"time": 0.5, "x": 66.57, "y": 14.22}, {"time": 0.5333, "x": 52.58, "y": 0.6}, {"time": 1, "x": -167.32, "y": 0.58}]}, "front-foot-target": {"rotate": [{"value": 18.19, "curve": [0.01, 11.17, 0.043, 1.37]}, {"time": 0.1, "value": 0.47}, {"time": 0.2333, "value": 0.55, "curve": [0.364, 0.3, 0.515, -80.48]}, {"time": 0.7333, "value": -80.78, "curve": [0.788, -80.38, 0.921, 17.42]}, {"time": 1, "value": 18.19}], "translate": [{"x": 139.21, "y": 22.94, "curve": [0.025, 139.21, 0.069, 111.46, 0.031, 3.25, 0.075, 0.06]}, {"time": 0.1, "x": 96.69, "y": 0.06}, {"time": 0.5, "x": -94.87, "y": -0.03, "curve": [0.518, -106.82, 0.575, -152.56, 0.534, 5.42, 0.557, 38.46]}, {"time": 0.6, "x": -152.56, "y": 57.05, "curve": [0.633, -152.56, 0.688, -128.05, 0.643, 75.61, 0.7, 84.14]}, {"time": 0.7333, "x": -109.42, "y": 84.14, "curve": [0.771, -93.91, 0.832, -30.64, 0.787, 84.14, 0.799, 89.65]}, {"time": 0.8667, "x": 17, "y": 75.25, "curve": [0.903, 66.18, 0.967, 139.21, 0.932, 61.53, 0.967, 44.02]}, {"time": 1, "x": 139.21, "y": 22.94}]}, "hip": {"rotate": [{"value": -4.35}], "translate": [{"x": -2.86, "y": -13.86, "curve": [0.025, -2.84, 0.067, -2.82, 0.028, -19.14, 0.054, -24.02]}, {"time": 0.1, "x": -2.61, "y": -24.19, "curve": [0.143, -2.34, 0.202, -1.79, 0.152, -23.98, 0.213, -14.81]}, {"time": 0.2667, "x": -1.21, "y": -7.12, "curve": [0.308, -0.86, 0.345, -0.51, 0.306, -1.63, 0.341, 3.15]}, {"time": 0.3667, "x": -0.33, "y": 3.15, "curve": [0.41, 0.02, 0.458, 0.26, 0.427, 3.3, 0.481, -6.75]}, {"time": 0.5, "x": 0.26, "y": -10.59, "curve": [0.553, 0.26, 0.559, 0.2, 0.519, -14.41, 0.548, -23.88]}, {"time": 0.6, "x": -0.17, "y": -23.71, "curve": [0.663, -0.72, 0.798, -2.09, 0.702, -23.36, 0.802, 3.53]}, {"time": 0.8667, "x": -2.46, "y": 3.48, "curve": [0.901, -2.63, 0.967, -2.87, 0.913, 3.45, 0.967, -7.64]}, {"time": 1, "x": -2.86, "y": -13.86}]}, "front-foot-tip": {"rotate": [{"value": 28.96, "curve": [0.056, 28.74, 0.049, 19.6]}, {"time": 0.0667, "value": 1.68}, {"time": 0.5, "value": -10, "curve": [0.525, -10, 0.592, -54.69]}, {"time": 0.6, "value": -59.66, "curve": [0.623, -74.54, 0.674, -101.78]}, {"time": 0.7333, "value": -101.78, "curve": [0.812, -101.78, 0.855, -84.67]}, {"time": 0.8667, "value": -63.53, "curve": [0.869, -58.38, 0.975, 28.96]}, {"time": 1, "value": 28.96}]}, "torso": {"rotate": [{"value": -20.72, "curve": [0.025, -20.57, 0.071, -20.04]}, {"time": 0.1333, "value": -20.04, "curve": [0.187, -20.04, 0.285, -21.16]}, {"time": 0.3667, "value": -21.16, "curve": [0.405, -21.16, 0.47, -20.9]}, {"time": 0.5, "value": -20.71, "curve": [0.518, -20.6, 0.582, -20.03]}, {"time": 0.6333, "value": -20.04, "curve": [0.709, -20.05, 0.815, -21.18]}, {"time": 0.8667, "value": -21.18, "curve": [0.908, -21.18, 0.971, -20.93]}, {"time": 1, "value": -20.72}]}, "neck": {"rotate": [{"value": 17.78, "curve": [0.025, 17.93, 0.071, 18.46]}, {"time": 0.1333, "value": 18.46, "curve": [0.187, 18.46, 0.285, 17.34]}, {"time": 0.3667, "value": 17.34, "curve": [0.405, 17.34, 0.47, 17.6]}, {"time": 0.5, "value": 17.79, "curve": [0.518, 17.9, 0.582, 18.47]}, {"time": 0.6333, "value": 18.46, "curve": [0.709, 18.45, 0.815, 17.32]}, {"time": 0.8667, "value": 17.32, "curve": [0.908, 17.32, 0.971, 17.57]}, {"time": 1, "value": 17.78}]}, "head": {"rotate": [{"value": -12.23, "curve": [0.061, -12.23, 0.191, -7.45]}, {"time": 0.2667, "value": -7.43, "curve": [0.341, -7.42, 0.421, -12.23]}, {"time": 0.5, "value": -12.23, "curve": [0.567, -12.26, 0.694, -7.46]}, {"time": 0.7667, "value": -7.47, "curve": [0.853, -7.49, 0.943, -12.23]}, {"time": 1, "value": -12.23}], "scale": [{"curve": [0.039, 1, 0.084, 0.991, 0.039, 1, 0.084, 1.019]}, {"time": 0.1333, "x": 0.991, "y": 1.019, "curve": [0.205, 0.991, 0.318, 1.019, 0.205, 1.019, 0.337, 0.992]}, {"time": 0.4, "x": 1.019, "y": 0.992, "curve": [0.456, 1.019, 0.494, 1.001, 0.483, 0.991, 0.493, 0.999]}, {"time": 0.5, "curve": [0.508, 0.998, 0.584, 0.991, 0.51, 1.002, 0.584, 1.019]}, {"time": 0.6333, "x": 0.991, "y": 1.019, "curve": [0.705, 0.991, 0.818, 1.019, 0.705, 1.019, 0.837, 0.992]}, {"time": 0.9, "x": 1.019, "y": 0.992, "curve": [0.956, 1.019, 0.955, 1, 0.983, 0.991, 0.955, 1]}, {"time": 1}]}, "back-foot-tip": {"rotate": [{"value": 4.09}, {"time": 0.0333, "value": 3.05}, {"time": 0.1, "value": -59.01, "curve": [0.124, -72.97, 0.169, -100.05]}, {"time": 0.2333, "value": -99.71, "curve": [0.326, -99.21, 0.349, -37.4]}, {"time": 0.3667, "value": -17.85, "curve": [0.388, 4.74, 0.451, 32.35]}, {"time": 0.5, "value": 32.4, "curve": [0.537, 32.44, 0.566, 6.43]}, {"time": 0.5667, "value": 2}, {"time": 1, "value": 4.09}]}, "front-thigh": {"translate": [{"x": 17.15, "y": -0.09, "curve": [0.178, 17.14, 0.295, -4.26, 0.009, -0.09, 0.475, 0.02]}, {"time": 0.5, "x": -4.26, "y": 0.02, "curve": [0.705, -4.27, 0.848, 17.15, 0.525, 0.02, 0.975, -0.09]}, {"time": 1, "x": 17.15, "y": -0.09}]}, "rear-thigh": {"translate": [{"x": -17.71, "y": -4.63, "curve": [0.036, -19.81, 0.043, -20.86, 0.036, -4.63, 0.05, -7.03]}, {"time": 0.1, "x": -20.95, "y": -7.06, "curve": [0.162, -21.05, 0.4, 7.79, 0.2, -7.13, 0.4, -1.9]}, {"time": 0.5, "x": 7.79, "y": -1.94, "curve": [0.612, 7.69, 0.875, -10.49, 0.592, -1.97, 0.917, -3.25]}, {"time": 1, "x": -17.71, "y": -4.63}]}, "torso2": {"rotate": [{"value": 1, "curve": [0.006, 1.2, 0.084, 2.88]}, {"time": 0.1333, "value": 2.88, "curve": [0.205, 2.88, 0.284, -1.17]}, {"time": 0.3667, "value": -1.17, "curve": [0.411, -1.17, 0.481, 0.57]}, {"time": 0.5, "value": 1, "curve": [0.515, 1.33, 0.59, 2.83]}, {"time": 0.6333, "value": 2.85, "curve": [0.683, 2.86, 0.796, -1.2]}, {"time": 0.8667, "value": -1.2, "curve": [0.916, -1.2, 0.984, 0.62]}, {"time": 1, "value": 1}]}, "torso3": {"rotate": [{"value": -1.81}]}, "front-upper-arm": {"rotate": [{"value": -9.51, "curve": [0.021, -13.32, 0.058, -19.4]}, {"time": 0.1, "value": -19.4, "curve": [0.238, -19.69, 0.337, 7.78]}, {"time": 0.3667, "value": 16.2, "curve": [0.399, 25.42, 0.497, 60.19]}, {"time": 0.6, "value": 60.26, "curve": [0.719, 60.13, 0.845, 27.61]}, {"time": 0.8667, "value": 22.45, "curve": [0.892, 16.38, 0.979, -3.27]}, {"time": 1, "value": -9.51}]}, "front-bracer": {"rotate": [{"value": 13.57, "curve": [0.022, 9.71, 0.147, -3.78]}, {"time": 0.3667, "value": -3.69, "curve": [0.457, -3.66, 0.479, 0.83]}, {"time": 0.5, "value": 4.05, "curve": [0.513, 6.08, 0.635, 30.8]}, {"time": 0.8, "value": 30.92, "curve": [0.974, 31, 0.98, 18.35]}, {"time": 1, "value": 13.57}]}, "front-fist": {"rotate": [{"value": -28.72, "curve": [0.024, -31.74, 0.176, -43.4]}, {"time": 0.3667, "value": -43.6, "curve": [0.403, -43.65, 0.47, -40.15]}, {"time": 0.5, "value": -35.63, "curve": [0.547, -28.59, 0.624, -4.57]}, {"time": 0.7333, "value": -4.59, "curve": [0.891, -4.62, 0.954, -24.28]}, {"time": 1, "value": -28.48}]}, "rear-upper-arm": {"rotate": [{"value": 28.28, "curve": [0.034, 30.94, 0.068, 32.05]}, {"time": 0.1, "value": 31.88, "curve": [0.194, 31.01, 0.336, -0.11]}, {"time": 0.3667, "value": -7.11, "curve": [0.421, -19.73, 0.53, -46.21]}, {"time": 0.6, "value": -45.75, "curve": [0.708, -45.03, 0.844, -13.56]}, {"time": 0.8667, "value": -6.48, "curve": [0.909, 6.59, 0.958, 24.21]}, {"time": 1, "value": 28.28}]}, "hair2": {"rotate": [{"value": -2.79, "curve": [0.074, -2.84, 0.121, 25.08]}, {"time": 0.2333, "value": 24.99, "curve": [0.35, 24.89, 0.427, -2.86]}, {"time": 0.5, "value": -2.8, "curve": [0.575, -2.73, 0.652, 24.5]}, {"time": 0.7333, "value": 24.55, "curve": [0.828, 24.6, 0.932, -2.69]}, {"time": 1, "value": -2.79}]}, "hair4": {"rotate": [{"value": -6.01, "curve": [0.106, -5.97, 0.151, 18.62]}, {"time": 0.2333, "value": 18.72, "curve": [0.336, 18.7, 0.405, -11.37]}, {"time": 0.5, "value": -11.45, "curve": [0.626, -11.46, 0.629, 18.94]}, {"time": 0.7333, "value": 18.92, "curve": [0.833, 18.92, 0.913, -6.06]}, {"time": 1, "value": -6.01}], "translate": [{"x": 0.03, "y": 1.35}]}, "rear-bracer": {"rotate": [{"value": 10.06, "curve": [0.044, 11.16, 0.063, 11.49]}, {"time": 0.1, "value": 11.49, "curve": [0.215, 11.49, 0.336, 2.92]}, {"time": 0.3667, "value": 0.84, "curve": [0.416, -2.52, 0.498, -10.84]}, {"time": 0.6, "value": -10.83, "curve": [0.762, -10.71, 0.845, -3.05]}, {"time": 0.8667, "value": -1.34, "curve": [0.917, 2.54, 0.977, 8.81]}, {"time": 1, "value": 10.06}]}, "gun": {"rotate": [{"value": -14.67, "curve": [0.086, -14.67, 0.202, 8.31]}, {"time": 0.2333, "value": 12.14, "curve": [0.279, 17.71, 0.391, 25.79]}, {"time": 0.5, "value": 25.77, "curve": [0.631, 25.74, 0.694, 4.53]}, {"time": 0.7333, "value": -0.65, "curve": [0.768, -5.21, 0.902, -14.4]}, {"time": 1, "value": -14.67}]}, "front-leg-target": {"translate": [{"x": -2.83, "y": -8.48, "curve": [0.008, -2.83, 0.058, 0.09, 0.001, 4.97, 0.058, 6.68]}, {"time": 0.0667, "x": 0.09, "y": 6.68, "curve": [0.3, 0.09, 0.767, -2.83, 0.3, 6.68, 0.767, -8.48]}, {"time": 1, "x": -2.83, "y": -8.48}]}, "hair1": {"rotate": [{"curve": [0.028, 1.24, 0.016, 3.46]}, {"time": 0.1, "value": 3.45, "curve": [0.159, 3.45, 0.189, 0.23]}, {"time": 0.2333, "value": -2.29, "curve": [0.265, -4.32, 0.305, -5.92]}, {"time": 0.3667, "value": -5.94, "curve": [0.446, -5.96, 0.52, 3.41]}, {"time": 0.6, "value": 3.42, "curve": [0.717, 3.42, 0.772, -5.93]}, {"time": 0.8667, "value": -5.97, "curve": [0.933, -5.99, 0.982, -0.94]}, {"time": 1}]}, "hair3": {"rotate": [{"curve": [0.067, 0, 0.159, -10.48]}, {"time": 0.2333, "value": -10.49, "curve": [0.334, -10.5, 0.439, -0.09]}, {"time": 0.5, "value": -0.09, "curve": [0.569, -0.09, 0.658, -10.75]}, {"time": 0.7333, "value": -10.7, "curve": [0.833, -10.63, 0.947, 0]}, {"time": 1}]}, "gun-tip": {"rotate": [{"time": 0.2333, "value": 0.11}]}, "muzzle-ring": {"rotate": [{"time": 0.2333, "value": 0.11}]}, "muzzle-ring2": {"rotate": [{"time": 0.2667, "value": 0.11}]}, "muzzle-ring3": {"rotate": [{"time": 0.2667, "value": 0.11}]}, "muzzle-ring4": {"rotate": [{"time": 0.2667, "value": 0.11}]}, "back-shoulder": {"translate": [{"x": -0.18, "y": -4.49, "curve": [0.133, -0.18, 0.333, 7.69, 0.133, -4.49, 0.333, 2.77]}, {"time": 0.4667, "x": 7.69, "y": 2.77, "curve": [0.6, 7.69, 0.858, -0.18, 0.6, 2.77, 0.858, -4.49]}, {"time": 1, "x": -0.18, "y": -4.49}]}, "front-shoulder": {"translate": [{"x": 1.46, "y": 9.37, "curve": [0.162, 1.41, 0.333, -1.66, 0.162, 9.37, 0.301, -7.23]}, {"time": 0.5, "x": -1.6, "y": -7.27, "curve": [0.735, -1.5, 0.847, 1.46, 0.723, -7.31, 0.838, 9.32]}, {"time": 1, "x": 1.46, "y": 9.37}]}, "head-control": {"translate": [{"x": -6.46, "y": -8.4, "curve": [0.053, -5.31, 0.167, -3.64, 0.093, -8.4, 0.196, -3.81]}, {"time": 0.2333, "x": -3.64, "y": -1.32, "curve": [0.309, -3.64, 0.436, -5.84, 0.275, 1.43, 0.38, 10.3]}, {"time": 0.5, "x": -7.03, "y": 10.29, "curve": [0.538, -7.75, 0.66, -10.54, 0.598, 10.27, 0.694, 1.56]}, {"time": 0.7333, "x": -10.54, "y": -1.26, "curve": [0.797, -10.54, 0.933, -7.91, 0.768, -3.79, 0.875, -8.4]}, {"time": 1, "x": -6.46, "y": -8.4}]}}, "ik": {"front-leg-ik": [{"softness": 25.7, "bendPositive": false, "curve": [0.008, 1, 0.025, 1, 0.008, 25.7, 0.025, 9.9]}, {"time": 0.0333, "softness": 9.9, "bendPositive": false, "curve": [0.15, 1, 0.383, 1, 0.15, 9.9, 0.383, 43.2]}, {"time": 0.5, "softness": 43.2, "bendPositive": false, "curve": [0.625, 1, 0.875, 1, 0.625, 43.2, 0.846, 45.57]}, {"time": 1, "softness": 25.7, "bendPositive": false}], "rear-leg-ik": [{"softness": 5, "bendPositive": false}, {"time": 0.4333, "softness": 4.9, "bendPositive": false}, {"time": 0.5, "softness": 28.81, "bendPositive": false}, {"time": 0.6, "softness": 43.8, "bendPositive": false}, {"time": 1, "softness": 5, "bendPositive": false}]}, "events": [{"name": "footstep"}, {"time": 0.5, "name": "footstep"}]}}}
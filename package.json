{"name": "phaser-editor-template-vite-ts", "description": "A Phaser 3 TypeScript template using Vite.", "version": "1.2.1", "repository": {"type": "git", "url": "git+https://github.com/phaserjs/template-vite-ts.git"}, "author": "Phaser Studio <<EMAIL>> (https://phaser.io/)", "license": "MIT", "licenseUrl": "http://www.opensource.org/licenses/mit-license.php", "bugs": {"url": "https://github.com/phaserjs/template-vite-ts/issues"}, "homepage": "https://github.com/phaserjs/template-vite-ts#readme", "scripts": {"start": "vite --config vite/config.dev.mjs", "build": "vite build --config vite/config.prod.mjs && phaser-asset-pack-hashing -j -r dist"}, "devDependencies": {"phaser-asset-pack-hashing": "^1.0.6", "terser": "^5.28.1", "typescript": "^5.3.3", "vite": "^5.1.4"}, "dependencies": {"@esotericsoftware/spine-phaser": "^4.1.55", "@phaserjs/editor-scripts-base": "^1.0.0", "phaser": "3.80.1"}}